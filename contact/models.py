from django.db import models

class ContactMessage(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100, verbose_name="الاسم")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    phone = models.Char<PERSON>ield(max_length=20, blank=True, verbose_name="رقم الهاتف")
    subject = models.CharField(max_length=200, verbose_name="الموضوع")
    message = models.TextField(verbose_name="الرسالة")

    # Status tracking
    is_read = models.BooleanField(default=False, verbose_name="مقروءة")
    is_replied = models.BooleanField(default=False, verbose_name="تم الرد")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإرسال")

    class Meta:
        ordering = ['-created_at']
        verbose_name = "رسالة اتصال"
        verbose_name_plural = "رسائل الاتصال"

    def __str__(self):
        return f"{self.name} - {self.subject}"

class ProjectEstimation(models.Model):
    PROJECT_TYPES = [
        ('ios_app', 'تطبيق iOS'),
        ('android_app', 'تطبيق Android'),
        ('web_app', 'تطبيق ويب'),
        ('website', 'موقع إلكتروني'),
        ('management_system', 'نظام إدارة'),
        ('ecommerce', 'متجر إلكتروني'),
        ('other', 'أخرى'),
    ]

    BUDGET_RANGES = [
        ('under_5k', 'أقل من 5,000 درهم'),
        ('5k_10k', '5,000 - 10,000 درهم'),
        ('10k_25k', '10,000 - 25,000 درهم'),
        ('25k_50k', '25,000 - 50,000 درهم'),
        ('50k_100k', '50,000 - 100,000 درهم'),
        ('over_100k', 'أكثر من 100,000 درهم'),
        ('not_sure', 'غير محدد'),
    ]

    DURATION_RANGES = [
        ('1_month', 'شهر واحد'),
        ('2_3_months', '2-3 أشهر'),
        ('3_6_months', '3-6 أشهر'),
        ('6_12_months', '6-12 شهر'),
        ('over_year', 'أكثر من سنة'),
        ('flexible', 'مرن'),
    ]

    # Client information
    name = models.CharField(max_length=100, verbose_name="اسم العميل")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    company = models.CharField(max_length=100, blank=True, verbose_name="الشركة")

    # Project details
    project_type = models.CharField(max_length=20, choices=PROJECT_TYPES, verbose_name="نوع المشروع")
    project_title = models.CharField(max_length=200, verbose_name="عنوان المشروع")
    project_description = models.TextField(verbose_name="وصف المشروع")
    key_features = models.TextField(verbose_name="الميزات الأساسية")
    target_audience = models.CharField(max_length=200, blank=True, verbose_name="الجمهور المستهدف")

    # Budget and timeline
    budget_range = models.CharField(max_length=20, choices=BUDGET_RANGES, blank=True, verbose_name="نطاق الميزانية")
    duration_range = models.CharField(max_length=20, choices=DURATION_RANGES, blank=True, verbose_name="المدة المطلوبة")

    # Additional files
    attachment = models.FileField(upload_to='project_estimations/', blank=True, verbose_name="ملف مرفق")

    # Status tracking
    is_processed = models.BooleanField(default=False, verbose_name="تم المعالجة")
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="التكلفة المقدرة")
    estimated_duration = models.CharField(max_length=100, blank=True, verbose_name="المدة المقدرة")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الطلب")

    class Meta:
        ordering = ['-created_at']
        verbose_name = "طلب تقدير مشروع"
        verbose_name_plural = "طلبات تقدير المشاريع"

    def __str__(self):
        return f"{self.name} - {self.project_title}"
