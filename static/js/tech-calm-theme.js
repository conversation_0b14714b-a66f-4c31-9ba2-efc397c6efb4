/**
 * Tech Calm Theme JavaScript
 * ثيم الهدوء التكنولوجي - سكريبت مخصص للأنيميشن الخطي والتفاعلات
 */

document.addEventListener('DOMContentLoaded', function() {
    initTechCalmTheme();
});

function initTechCalmTheme() {
    initNavbarEffects();
    initLinearAnimations();
    initScrollAnimations();
    initButtonEffects();
    initCardInteractions();
    initGeometricElements();
}

// ===== NAVBAR EFFECTS - تأثيرات شريط التصفح =====
function initNavbarEffects() {
    const navbar = document.querySelector('.tech-calm-navbar');
    const navLinks = document.querySelectorAll('.navbar-link');
    
    if (!navbar) return;
    
    // تأثير التمرير على شريط التصفح
    let lastScrollTop = 0;
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        lastScrollTop = scrollTop;
    });
    
    // تفعيل الرابط النشط
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
    
    // تأثير النقر على الروابط
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // إذا كان رابط داخلي (يبدأ بـ #)
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    smoothScrollTo(target);
                }
            }
        });
    });
}

// ===== LINEAR ANIMATIONS - الأنيميشن الخطي =====
function initLinearAnimations() {
    // أنيميشن خطي للعناصر عند التحميل
    const animatedElements = document.querySelectorAll('[data-animate]');
    
    animatedElements.forEach((element, index) => {
        const animationType = element.getAttribute('data-animate');
        const delay = index * 100; // تأخير متدرج
        
        setTimeout(() => {
            element.classList.add(`animate-${animationType}`);
        }, delay);
    });
    
    // أنيميشن خطي للخطوط الفاصلة
    createLinearSeparators();
}

// ===== SCROLL ANIMATIONS - أنيميشن التمرير =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.getAttribute('data-scroll-animate') || 'fade-up';
                
                element.classList.add(`animate-${animationType}`);
                observer.unobserve(element);
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر للأنيميشن عند التمرير
    const scrollElements = document.querySelectorAll('[data-scroll-animate]');
    scrollElements.forEach(element => {
        observer.observe(element);
    });
}

// ===== BUTTON EFFECTS - تأثيرات الأزرار =====
function initButtonEffects() {
    const primaryButtons = document.querySelectorAll('.btn-tech-primary');
    const secondaryButtons = document.querySelectorAll('.btn-tech-secondary');
    
    // تأثير الموجة للأزرار الأساسية
    primaryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            createRippleEffect(this, e);
        });
    });
    
    // تأثير التوهج للأزرار الثانوية
    secondaryButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 20px rgba(0, 188, 212, 0.3)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.boxShadow = 'none';
        });
    });
}

// ===== CARD INTERACTIONS - تفاعلات البطاقات =====
function initCardInteractions() {
    const cards = document.querySelectorAll('.tech-calm-card');
    
    cards.forEach(card => {
        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        // تأثير النقر
        card.addEventListener('click', function() {
            this.style.transform = 'translateY(-8px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'translateY(-12px) scale(1.02)';
            }, 150);
        });
    });
}

// ===== GEOMETRIC ELEMENTS - العناصر الهندسية =====
function initGeometricElements() {
    createFloatingShapes();
    animateGeometricSeparators();
}

// ===== UTILITY FUNCTIONS - دوال مساعدة =====

// تمرير سلس إلى عنصر
function smoothScrollTo(target) {
    const targetPosition = target.offsetTop - 80; // مع مراعاة ارتفاع شريط التصفح
    
    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });
}

// إنشاء تأثير الموجة
function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
    `;
    
    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// إنشاء خطوط فاصلة خطية
function createLinearSeparators() {
    const separators = document.querySelectorAll('.geometric-separator');
    
    separators.forEach((separator, index) => {
        setTimeout(() => {
            separator.style.transform = 'scaleX(1)';
            separator.style.transformOrigin = 'left';
            separator.style.transition = 'transform 0.8s ease-out';
        }, index * 200);
    });
}

// أنيميشن الفواصل الهندسية
function animateGeometricSeparators() {
    const separators = document.querySelectorAll('.geometric-separator');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const separator = entry.target;
                separator.style.animation = 'expandLine 1s ease-out forwards';
                observer.unobserve(separator);
            }
        });
    });
    
    separators.forEach(separator => {
        observer.observe(separator);
    });
}

// إنشاء أشكال هندسية عائمة
function createFloatingShapes() {
    const shapesContainer = document.createElement('div');
    shapesContainer.className = 'floating-shapes-container';
    shapesContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
        overflow: hidden;
    `;
    
    document.body.appendChild(shapesContainer);
    
    // إنشاء أشكال هندسية بسيطة
    for (let i = 0; i < 5; i++) {
        createFloatingShape(shapesContainer, i);
    }
}

// إنشاء شكل هندسي عائم
function createFloatingShape(container, index) {
    const shape = document.createElement('div');
    const size = Math.random() * 100 + 50;
    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;
    
    shape.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: linear-gradient(45deg, rgba(0, 188, 212, 0.1), rgba(0, 255, 127, 0.05));
        border-radius: ${Math.random() > 0.5 ? '50%' : '0'};
        animation: floatShape ${10 + index * 2}s infinite linear;
        transform-origin: center;
    `;
    
    container.appendChild(shape);
}

// إضافة CSS للأنيميشن
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    @keyframes expandLine {
        from {
            transform: scaleX(0);
        }
        to {
            transform: scaleX(1);
        }
    }
    
    @keyframes floatShape {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 0.1;
        }
        50% {
            opacity: 0.3;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// تحسين الأداء - تقليل معدل تحديث الأحداث
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// تطبيق التحسين على أحداث التمرير
window.addEventListener('scroll', throttle(() => {
    // معالجة أحداث التمرير المحسنة
}, 16)); // 60fps
