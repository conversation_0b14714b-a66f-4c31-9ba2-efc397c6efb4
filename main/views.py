from django.shortcuts import render
from django.http import JsonResponse
from .models import CompanyInfo, Service, Testimonial
from portfolio.models import Project
from team.models import TeamMember

def home(request):
    """الصفحة الرئيسية"""
    context = {
        'company_info': CompanyInfo.objects.first(),
        'services': Service.objects.filter(is_active=True)[:6],
        'featured_projects': Project.objects.filter(featured=True)[:6],
        'testimonials': Testimonial.objects.filter(is_active=True, is_featured=True)[:3],
        'team_members': TeamMember.objects.filter(is_active=True)[:4],
    }
    return render(request, 'main/home.html', context)

def about(request):
    """صفحة من نحن"""
    context = {
        'company_info': CompanyInfo.objects.first(),
        'team_members': TeamMember.objects.filter(is_active=True),
        'services': Service.objects.filter(is_active=True),
    }
    return render(request, 'main/about.html', context)

def tech_calm_demo(request):
    """عرض توضيحي لثيم الهدوء التكنولوجي"""
    return render(request, 'tech-calm-demo.html')
