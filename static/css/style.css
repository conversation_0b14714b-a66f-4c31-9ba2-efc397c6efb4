/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== ABSTRACT BACKGROUND ===== */
.abstract-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    backdrop-filter: blur(10px);
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.1));
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
    background: linear-gradient(45deg, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.1));
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    right: 30%;
    animation-delay: -10s;
    background: linear-gradient(45deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
}

.shape-4 {
    width: 250px;
    height: 250px;
    bottom: 20%;
    left: 20%;
    animation-delay: -15s;
    background: linear-gradient(45deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1));
}

.shape-5 {
    width: 100px;
    height: 100px;
    top: 80%;
    left: 60%;
    animation-delay: -8s;
    background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
    }
    66% {
        transform: translateY(30px) rotate(240deg);
    }
    100% {
        transform: translateY(0px) rotate(360deg);
    }
}

/* ===== NAVIGATION ===== */
.glass-nav {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.glass-nav.scrolled {
    background: rgba(31, 41, 55, 0.95);
    backdrop-filter: blur(20px);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-shape {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.logo-shape::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 6px;
    transform: translate(-50%, -50%) rotate(45deg);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    background: var(--gradient-primary);
    color: white !important;
}

/* ===== BUTTONS ===== */
.btn-primary-custom {
    background: var(--gradient-primary);
    border: none;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
}

.btn-primary-custom:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-secondary-custom {
    background: transparent;
    border: 2px solid white;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
}

.btn-secondary-custom:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
}

/* ===== CARDS ===== */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 2rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.glass-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    background: rgba(255, 255, 255, 0.15);
}

/* ===== SECTIONS ===== */
.section-padding {
    padding: 100px 0;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #fff 0%, rgba(255,255,255,0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== FOOTER ===== */
.footer-section {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: white;
    padding: 60px 0 20px;
    margin-top: 100px;
}

.footer-brand .logo-text {
    font-size: 2rem;
}

.footer-description {
    color: rgba(255, 255, 255, 0.7);
    margin-top: 1rem;
    line-height: 1.6;
}

.footer-title {
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: white;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.contact-info p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.5rem;
}

.contact-info i {
    color: var(--primary-color);
    margin-left: 10px;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.copyright {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9rem;
}

/* ===== HOME PAGE STYLES ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white;
}

.gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.counter {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.code-block {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    padding: 1rem;
    min-width: 300px;
    box-shadow: var(--shadow-heavy);
    animation: float 6s ease-in-out infinite;
}

.code-header {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot.red { background: #ff5f56; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #27ca3f; }

.code-content {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
}

.code-line {
    margin-bottom: 0.5rem;
}

.keyword { color: #ff79c6; }
.function { color: #50fa7b; }
.string { color: #f1fa8c; }

.tech-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.tech-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    animation: orbit 20s linear infinite;
    animation-delay: var(--delay);
}

.tech-icon:nth-child(2) { top: 10%; right: 20%; }
.tech-icon:nth-child(3) { top: 30%; left: 10%; }
.tech-icon:nth-child(4) { bottom: 30%; right: 10%; }
.tech-icon:nth-child(5) { bottom: 10%; left: 30%; }
.tech-icon:nth-child(6) { top: 50%; right: 5%; }

@keyframes orbit {
    0% { transform: rotate(0deg) translateX(50px) rotate(0deg); }
    100% { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Service Cards */
.service-card {
    text-align: center;
    padding: 2.5rem 2rem;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.service-title {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* Project Cards */
.project-card {
    overflow: hidden;
    padding: 0;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-link {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    transition: var(--transition);
}

.project-link:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
    color: white;
}

.project-content {
    padding: 1.5rem;
}

.project-title {
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Team Cards */
.team-card {
    text-align: center;
    padding: 2rem 1.5rem;
    height: 100%;
    animation-delay: var(--delay);
}

.team-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-avatar {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.team-name {
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-position {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.team-social {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.team-social a {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.team-social a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    color: white;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.cta-content {
    text-align: center;
    padding: 4rem 3rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .logo-text {
        font-size: 1.2rem !important;
    }

    .floating-shape {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .tech-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .code-block {
        min-width: 250px;
        font-size: 0.8rem;
    }
}
