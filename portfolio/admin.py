from django.contrib import admin
from .models import Technology, ProjectCategory, Project

@admin.register(Technology)
class TechnologyAdmin(admin.ModelAdmin):
    list_display = ['name', 'icon', 'color']
    search_fields = ['name']
    list_editable = ['icon', 'color']

@admin.register(ProjectCategory)
class ProjectCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name']

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'status', 'featured', 'order', 'created_at']
    list_filter = ['category', 'status', 'featured', 'technologies']
    search_fields = ['title', 'description', 'client']
    list_editable = ['featured', 'order', 'status']
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ['technologies']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'slug', 'category', 'status', 'featured', 'order')
        }),
        ('وصف المشروع', {
            'fields': ('short_description', 'description')
        }),
        ('الصور', {
            'fields': ('featured_image', 'gallery_image_1', 'gallery_image_2', 'gallery_image_3')
        }),
        ('الروابط', {
            'fields': ('live_url', 'github_url', 'app_store_url', 'play_store_url')
        }),
        ('تفاصيل المشروع', {
            'fields': ('client', 'duration', 'team_size', 'technologies')
        }),
    )
