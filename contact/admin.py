from django.contrib import admin
from .models import ContactMessage, ProjectEstimation

@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'subject', 'is_read', 'is_replied', 'created_at']
    list_filter = ['is_read', 'is_replied', 'created_at']
    search_fields = ['name', 'email', 'subject']
    list_editable = ['is_read', 'is_replied']
    readonly_fields = ['created_at']

    fieldsets = (
        ('معلومات المرسل', {
            'fields': ('name', 'email', 'phone')
        }),
        ('الرسالة', {
            'fields': ('subject', 'message')
        }),
        ('حالة الرسالة', {
            'fields': ('is_read', 'is_replied', 'created_at')
        }),
    )

@admin.register(ProjectEstimation)
class ProjectEstimationAdmin(admin.ModelAdmin):
    list_display = ['name', 'project_title', 'project_type', 'budget_range', 'is_processed', 'created_at']
    list_filter = ['project_type', 'budget_range', 'duration_range', 'is_processed', 'created_at']
    search_fields = ['name', 'email', 'project_title', 'project_description']
    list_editable = ['is_processed']
    readonly_fields = ['created_at']

    fieldsets = (
        ('معلومات العميل', {
            'fields': ('name', 'email', 'phone', 'company')
        }),
        ('تفاصيل المشروع', {
            'fields': ('project_type', 'project_title', 'project_description', 'key_features', 'target_audience')
        }),
        ('الميزانية والجدول الزمني', {
            'fields': ('budget_range', 'duration_range')
        }),
        ('ملفات مرفقة', {
            'fields': ('attachment',)
        }),
        ('التقدير والمعالجة', {
            'fields': ('is_processed', 'estimated_cost', 'estimated_duration', 'notes')
        }),
        ('معلومات إضافية', {
            'fields': ('created_at',)
        }),
    )
