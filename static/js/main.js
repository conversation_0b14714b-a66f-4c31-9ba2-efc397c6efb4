// ===== MAIN JAVASCRIPT FILE =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll) with optimized settings
    AOS.init({
        duration: 600,
        easing: 'ease-out-cubic',
        once: true,
        offset: 50,
        delay: 0,
        disable: function() {
            // Disable on mobile for better performance
            return window.innerWidth < 768;
        }
    });

    // Initialize essential interactions only
    initScrollAnimations();
    initNavbarEffects();
    initSimpleScrollEffects();
    initCounterAnimations();
    initSmoothScrolling();
    initFormAnimations();
    initSimpleTextEffects();
    initImageLazyLoading();
});

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all elements with animation classes
    document.querySelectorAll('.fade-in, .slide-up, .slide-left, .slide-right, .scale-in').forEach(el => {
        observer.observe(el);
    });
}

// ===== NAVBAR EFFECTS =====
function initNavbarEffects() {
    const navbar = document.querySelector('.glass-nav');
    let lastScrollTop = 0;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add scrolled class when scrolling down
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Active nav link highlighting
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section[id]');

    window.addEventListener('scroll', () => {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href').includes(current)) {
                link.classList.add('active');
            }
        });
    });
}

// ===== SIMPLE SCROLL EFFECTS =====
function initSimpleScrollEffects() {
    // Simplified scroll effects for better performance
    // Remove heavy parallax animations
}

// ===== COUNTER ANIMATIONS =====
function initCounterAnimations() {
    const counters = document.querySelectorAll('.counter');
    
    const countUp = (element) => {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            element.textContent = Math.floor(current);
            
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                countUp(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

// ===== SIMPLE TEXT EFFECTS =====
function initSimpleTextEffects() {
    // Simplified text effects - no typing animation for better performance
    const typingElements = document.querySelectorAll('.typing-effect');

    typingElements.forEach(element => {
        // Just show the text immediately
        element.style.opacity = '1';
    });
}

// ===== IMAGE LAZY LOADING =====
function initImageLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('loaded');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        images.forEach(img => img.classList.add('loaded'));
    }
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== FORM ANIMATIONS =====
function initFormAnimations() {
    const formGroups = document.querySelectorAll('.form-group');
    
    formGroups.forEach(group => {
        const input = group.querySelector('input, textarea, select');
        const label = group.querySelector('label');
        
        if (input && label) {
            // Float label effect
            input.addEventListener('focus', () => {
                label.classList.add('float');
                group.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    label.classList.remove('float');
                }
                group.classList.remove('focused');
            });
            
            // Check if input has value on load
            if (input.value) {
                label.classList.add('float');
            }
        }
    });

    // Form submission animation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
                submitBtn.disabled = true;
            }
        });
    });
}

// ===== CARD HOVER EFFECTS =====
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.glass-card, .project-card, .team-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// ===== UTILITY FUNCTIONS =====

// Add CSS animation classes
const style = document.createElement('style');
style.textContent = `
    @keyframes blink {
        0%, 50% { border-color: transparent; }
        51%, 100% { border-color: currentColor; }
    }
    
    .fade-in {
        opacity: 0;
        transition: opacity 0.8s ease-in-out;
    }
    
    .fade-in.animate-in {
        opacity: 1;
    }
    
    .slide-up {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s ease-out;
    }
    
    .slide-up.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .slide-left {
        opacity: 0;
        transform: translateX(-50px);
        transition: all 0.8s ease-out;
    }
    
    .slide-left.animate-in {
        opacity: 1;
        transform: translateX(0);
    }
    
    .slide-right {
        opacity: 0;
        transform: translateX(50px);
        transition: all 0.8s ease-out;
    }
    
    .slide-right.animate-in {
        opacity: 1;
        transform: translateX(0);
    }
    
    .scale-in {
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.8s ease-out;
    }
    
    .scale-in.animate-in {
        opacity: 1;
        transform: scale(1);
    }
    
    .form-group {
        position: relative;
        margin-bottom: 1.5rem;
    }
    
    .form-group label {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        transition: all 0.3s ease;
        pointer-events: none;
        color: rgba(255, 255, 255, 0.7);
    }
    
    .form-group label.float {
        top: -10px;
        right: 10px;
        font-size: 0.8rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0 5px;
        border-radius: 4px;
    }
    
    .form-group.focused label {
        color: var(--primary-color);
    }
`;
document.head.appendChild(style);

// Preloader
window.addEventListener('load', function() {
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.opacity = '0';
        setTimeout(() => {
            preloader.style.display = 'none';
        }, 500);
    }
});

// Performance optimization
let ticking = false;

function requestTick() {
    if (!ticking) {
        requestAnimationFrame(updateAnimations);
        ticking = true;
    }
}

function updateAnimations() {
    // Update animations here
    ticking = false;
}

window.addEventListener('scroll', requestTick);
