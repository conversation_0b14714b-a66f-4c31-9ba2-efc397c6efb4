/* ===== PORTFOLIO STYLES ===== */

/* Page Header */
.page-header {
    padding: 140px 0 100px;
    text-align: center;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    position: relative;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
}

.page-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Filters Section */
.filters-section {
    padding: 50px 0;
}

.filters-container {
    padding: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    justify-content: space-between;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-label {
    color: white;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.search-group {
    flex: 1;
    max-width: 300px;
}

.search-container {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 25px 0 0 25px;
    outline: none;
    transition: var(--transition);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
}

.search-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0 25px 25px 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--gradient-secondary);
}

/* Projects Grid */
.projects-grid-section {
    min-height: 60vh;
}

.project-item {
    transition: var(--transition);
}

.project-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.project-card {
    overflow: hidden;
    padding: 0;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: white;
    font-size: 2rem;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    transition: var(--transition);
}

.action-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
    color: white;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-content {
    padding: 1.5rem;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-category {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.project-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-completed {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-in_progress {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-planned {
    background: rgba(99, 102, 241, 0.2);
    color: #6366f1;
}

.project-title {
    margin-bottom: 1rem;
}

.project-title a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.project-title a:hover {
    color: var(--primary-color);
}

.project-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-technologies {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.tech-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.tech-more {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.project-client,
.project-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* No Projects */
.no-projects {
    padding: 4rem 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

.no-projects i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.4);
}

.no-projects h3 {
    color: white;
    margin-bottom: 1rem;
}

/* Pagination */
.pagination-nav {
    margin-top: 3rem;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.page-item {
    margin: 0;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: var(--transition);
}

.page-link:hover,
.page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .filters-container {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .search-group {
        max-width: none;
    }
    
    .project-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .project-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
