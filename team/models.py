from django.db import models

class TeamMember(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100, verbose_name="الاسم")
    position = models.CharField(max_length=100, verbose_name="المنصب")
    bio = models.TextField(verbose_name="نبذة شخصية")
    photo = models.ImageField(upload_to='team/', verbose_name="الصورة الشخصية")

    # Skills and expertise
    skills = models.TextField(help_text="مهارات مفصولة بفواصل", verbose_name="المهارات")
    experience_years = models.PositiveIntegerField(verbose_name="سنوات الخبرة")

    # Social links
    linkedin_url = models.URLField(blank=True, verbose_name="رابط LinkedIn")
    github_url = models.URLField(blank=True, verbose_name="رابط GitHub")
    twitter_url = models.URLField(blank=True, verbose_name="رابط Twitter")
    website_url = models.URLField(blank=True, verbose_name="الموقع الشخصي")

    # Display settings
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']
        verbose_name = "عضو الفريق"
        verbose_name_plural = "أعضاء الفريق"

    def __str__(self):
        return f"{self.name} - {self.position}"

    def get_skills_list(self):
        """Return skills as a list"""
        return [skill.strip() for skill in self.skills.split(',') if skill.strip()]
