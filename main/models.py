from django.db import models

class CompanyInfo(models.Model):
    """Singleton model for company information"""
    name = models.CharField(max_length=100, default="Codnet", verbose_name="اسم الشركة")
    tagline = models.CharField(max_length=200, verbose_name="الشعار")
    description = models.TextField(verbose_name="وصف الشركة")
    mission = models.TextField(verbose_name="المهمة")
    vision = models.TextField(verbose_name="الرؤية")

    # Contact information
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    address = models.TextField(verbose_name="العنوان")

    # Social media
    linkedin_url = models.URLField(blank=True, verbose_name="رابط LinkedIn")
    facebook_url = models.URLField(blank=True, verbose_name="رابط Facebook")
    twitter_url = models.URLField(blank=True, verbose_name="رابط Twitter")
    instagram_url = models.URLField(blank=True, verbose_name="رابط Instagram")

    # Logo and branding
    logo = models.ImageField(upload_to='branding/', blank=True, verbose_name="الشعار")
    favicon = models.ImageField(upload_to='branding/', blank=True, verbose_name="الأيقونة المفضلة")

    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "معلومات الشركة"
        verbose_name_plural = "معلومات الشركة"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Ensure only one instance exists
        if not self.pk and CompanyInfo.objects.exists():
            raise ValueError("يمكن إنشاء سجل واحد فقط لمعلومات الشركة")
        return super().save(*args, **kwargs)

class Service(models.Model):
    title = models.CharField(max_length=100, verbose_name="عنوان الخدمة")
    description = models.TextField(verbose_name="وصف الخدمة")
    icon = models.CharField(max_length=100, help_text="CSS class for icon", verbose_name="أيقونة")
    color = models.CharField(max_length=7, default="#000000", help_text="Hex color code", verbose_name="اللون")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'title']
        verbose_name = "خدمة"
        verbose_name_plural = "الخدمات"

    def __str__(self):
        return self.title

class Testimonial(models.Model):
    client_name = models.CharField(max_length=100, verbose_name="اسم العميل")
    client_position = models.CharField(max_length=100, verbose_name="منصب العميل")
    client_company = models.CharField(max_length=100, verbose_name="شركة العميل")
    client_photo = models.ImageField(upload_to='testimonials/', blank=True, verbose_name="صورة العميل")

    testimonial_text = models.TextField(verbose_name="نص الشهادة")
    rating = models.PositiveIntegerField(default=5, choices=[(i, i) for i in range(1, 6)], verbose_name="التقييم")

    is_featured = models.BooleanField(default=False, verbose_name="مميزة")
    is_active = models.BooleanField(default=True, verbose_name="نشطة")
    order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-is_featured', 'order', '-created_at']
        verbose_name = "شهادة عميل"
        verbose_name_plural = "شهادات العملاء"

    def __str__(self):
        return f"{self.client_name} - {self.client_company}"
