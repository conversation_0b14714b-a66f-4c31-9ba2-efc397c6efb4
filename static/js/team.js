// ===== TEAM PAGE JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    initTeamAnimations();
    initMemberModals();
    initCounterAnimations();
    initTeamInteractions();
});

// ===== TEAM ANIMATIONS =====
function initTeamAnimations() {
    // Stagger animation for team cards
    const teamCards = document.querySelectorAll('.team-member-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    teamCards.forEach(card => {
        observer.observe(card);
    });
    
    // Value cards animation
    const valueCards = document.querySelectorAll('.value-card');
    valueCards.forEach(card => {
        observer.observe(card);
    });
    
    // Stat cards animation
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        observer.observe(card);
    });
}

// ===== MEMBER MODALS =====
function initMemberModals() {
    const viewMoreButtons = document.querySelectorAll('.btn-view-more');
    const modal = document.getElementById('memberModal');
    const modalTitle = document.getElementById('memberModalLabel');
    const modalBody = document.getElementById('memberModalBody');
    
    viewMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const memberIndex = parseInt(this.getAttribute('data-member'));
            
            if (typeof teamMembersData !== 'undefined' && teamMembersData[memberIndex]) {
                const member = teamMembersData[memberIndex];
                showMemberModal(member, modal, modalTitle, modalBody);
            }
        });
    });
}

function showMemberModal(member, modal, modalTitle, modalBody) {
    modalTitle.textContent = member.name;
    
    const socialLinks = generateSocialLinks(member);
    const skillTags = member.skills.map(skill => 
        `<span class="skill-tag">${skill}</span>`
    ).join('');
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-4 text-center">
                ${member.photo ? 
                    `<img src="${member.photo}" alt="${member.name}" class="member-detail-photo">` :
                    `<div class="member-detail-photo placeholder-avatar">
                        <i class="fas fa-user"></i>
                    </div>`
                }
            </div>
            <div class="col-md-8">
                <h3 class="member-detail-name">${member.name}</h3>
                <p class="member-detail-position">${member.position}</p>
                
                <div class="member-experience mb-3">
                    <i class="fas fa-calendar-alt"></i>
                    <span>${member.experience_years} سنوات خبرة</span>
                </div>
                
                <p class="member-detail-bio">${member.bio}</p>
                
                <div class="member-detail-skills">
                    <h5>المهارات:</h5>
                    <div class="skills-list">
                        ${skillTags}
                    </div>
                </div>
                
                ${socialLinks ? `
                <div class="member-detail-social">
                    <h5>تواصل معي:</h5>
                    <div class="social-links">
                        ${socialLinks}
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;
    
    // Show modal with Bootstrap
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

function generateSocialLinks(member) {
    let links = '';
    
    if (member.linkedin_url) {
        links += `<a href="${member.linkedin_url}" target="_blank" class="social-link">
            <i class="fab fa-linkedin-in"></i>
        </a>`;
    }
    
    if (member.github_url) {
        links += `<a href="${member.github_url}" target="_blank" class="social-link">
            <i class="fab fa-github"></i>
        </a>`;
    }
    
    if (member.twitter_url) {
        links += `<a href="${member.twitter_url}" target="_blank" class="social-link">
            <i class="fab fa-twitter"></i>
        </a>`;
    }
    
    if (member.website_url) {
        links += `<a href="${member.website_url}" target="_blank" class="social-link">
            <i class="fas fa-globe"></i>
        </a>`;
    }
    
    return links;
}

// ===== COUNTER ANIMATIONS =====
function initCounterAnimations() {
    const counters = document.querySelectorAll('.counter');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.5
    });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        element.textContent = Math.floor(current);
        
        if (current >= target) {
            element.textContent = target;
            clearInterval(timer);
        }
    }, 16);
}

// ===== TEAM INTERACTIONS =====
function initTeamInteractions() {
    // Enhanced hover effects for team cards
    const teamCards = document.querySelectorAll('.team-member-card');
    
    teamCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add glow effect
            this.style.boxShadow = '0 25px 50px -12px rgba(99, 102, 241, 0.4)';
            
            // Animate skill tags
            const skillTags = this.querySelectorAll('.skill-tag');
            skillTags.forEach((tag, index) => {
                setTimeout(() => {
                    tag.style.transform = 'translateY(-3px)';
                    tag.style.background = 'rgba(99, 102, 241, 0.3)';
                }, index * 50);
            });
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
            
            // Reset skill tags
            const skillTags = this.querySelectorAll('.skill-tag');
            skillTags.forEach(tag => {
                tag.style.transform = 'translateY(0)';
                tag.style.background = 'rgba(255, 255, 255, 0.1)';
            });
        });
    });
    
    // Value cards hover effects
    const valueCards = document.querySelectorAll('.value-card');
    
    valueCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.4)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.value-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
                icon.style.boxShadow = 'none';
            }
        });
    });
    
    // Stat cards hover effects
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
                icon.style.boxShadow = '0 10px 25px rgba(99, 102, 241, 0.4)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1)';
                icon.style.boxShadow = 'none';
            }
        });
    });
}

// ===== PARALLAX EFFECTS =====
function initParallaxEffects() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        
        // Parallax for team member images
        const memberImages = document.querySelectorAll('.member-image img');
        memberImages.forEach((img, index) => {
            const rate = scrolled * (0.05 + index * 0.01);
            img.style.transform = `translateY(${rate}px)`;
        });
        
        // Parallax for value icons
        const valueIcons = document.querySelectorAll('.value-icon');
        valueIcons.forEach((icon, index) => {
            const rate = scrolled * (0.02 + index * 0.005);
            icon.style.transform = `translateY(${rate}px) rotate(${rate * 0.5}deg)`;
        });
    });
}

// Initialize parallax effects
initParallaxEffects();

// ===== TEAM MEMBER FILTERING =====
function initTeamFiltering() {
    // This can be extended to add filtering by position, skills, etc.
    const filterButtons = document.querySelectorAll('.team-filter-btn');
    const teamMembers = document.querySelectorAll('.team-member-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active state
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter team members
            teamMembers.forEach(member => {
                const memberPosition = member.getAttribute('data-position');
                
                if (filter === 'all' || memberPosition === filter) {
                    member.style.display = 'block';
                    setTimeout(() => {
                        member.classList.remove('hidden');
                    }, 100);
                } else {
                    member.classList.add('hidden');
                    setTimeout(() => {
                        member.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    const elements = document.querySelectorAll('.fade-in, .slide-up, .scale-in');
    
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                scrollObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    elements.forEach(element => {
        scrollObserver.observe(element);
    });
}

// Initialize scroll animations
initScrollAnimations();

// ===== PERFORMANCE OPTIMIZATION =====
let ticking = false;

function requestTick() {
    if (!ticking) {
        requestAnimationFrame(updateAnimations);
        ticking = true;
    }
}

function updateAnimations() {
    // Update scroll-based animations here
    ticking = false;
}

// Throttle scroll events
window.addEventListener('scroll', requestTick);

// ===== ACCESSIBILITY IMPROVEMENTS =====
function initAccessibility() {
    // Add keyboard navigation for team cards
    const teamCards = document.querySelectorAll('.team-member-card');
    
    teamCards.forEach(card => {
        card.setAttribute('tabindex', '0');
        
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const viewMoreBtn = this.querySelector('.btn-view-more');
                if (viewMoreBtn) {
                    viewMoreBtn.click();
                }
            }
        });
    });
    
    // Add ARIA labels
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        const icon = link.querySelector('i');
        if (icon) {
            const platform = icon.className.includes('linkedin') ? 'LinkedIn' :
                           icon.className.includes('github') ? 'GitHub' :
                           icon.className.includes('twitter') ? 'Twitter' :
                           icon.className.includes('globe') ? 'Website' : 'Social';
            link.setAttribute('aria-label', `Visit ${platform} profile`);
        }
    });
}

// Initialize accessibility features
initAccessibility();
