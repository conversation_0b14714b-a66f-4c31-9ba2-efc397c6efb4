/* ===== TEAM PAGE STYLES ===== */

/* Page Header */
.page-header {
    padding: 150px 0 100px;
    text-align: center;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.page-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: white;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Team Stats */
.team-stats-section {
    padding: 50px 0;
}

.stat-card {
    padding: 2.5rem 2rem;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: white;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    font-weight: 500;
}

/* Team Members */
.team-members-section {
    background: rgba(0, 0, 0, 0.05);
}

.team-member-card {
    padding: 0;
    overflow: hidden;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.team-member-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-heavy);
}

.member-image-container {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.member-image {
    width: 100%;
    height: 100%;
    position: relative;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.placeholder-avatar {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.team-member-card:hover .member-overlay {
    opacity: 1;
}

.team-member-card:hover .member-image img {
    transform: scale(1.1);
}

.member-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: white;
    color: var(--primary-color);
    transform: scale(1.1);
}

.member-content {
    padding: 2rem;
}

.member-name {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.member-position {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.member-experience {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.member-experience i {
    color: var(--accent-color);
}

.member-bio {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.member-skills h5 {
    color: white;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.skills-list {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.skill-tag {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.skill-more {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.btn-view-more {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.btn-view-more:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* No Team */
.no-team {
    padding: 4rem 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

.no-team i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.4);
}

.no-team h3 {
    color: white;
    margin-bottom: 1rem;
}

/* Team Values */
.team-values-section {
    background: rgba(0, 0, 0, 0.1);
}

.value-card {
    padding: 2.5rem 2rem;
    height: 100%;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.value-title {
    color: white;
    font-weight: 600;
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.value-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Join Team */
.join-team-section {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.join-team-content {
    text-align: center;
    padding: 4rem 3rem;
}

.join-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.join-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.join-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Modal */
.glass-modal {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    color: white;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    color: white;
    font-weight: 600;
}

.btn-close {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    opacity: 1;
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.member-detail-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 1.5rem;
    display: block;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.member-detail-name {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-align: center;
    margin-bottom: 0.5rem;
}

.member-detail-position {
    font-size: 1.2rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 600;
}

.member-detail-bio {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.member-detail-skills {
    margin-bottom: 2rem;
}

.member-detail-skills h5 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.member-detail-social {
    text-align: center;
}

.member-detail-social h5 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.member-detail-social .social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .member-social {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .join-title {
        font-size: 2rem;
    }
    
    .join-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .join-team-content {
        padding: 3rem 2rem;
    }
    
    .value-card,
    .stat-card,
    .team-member-card .member-content {
        padding: 2rem 1.5rem;
    }
}
