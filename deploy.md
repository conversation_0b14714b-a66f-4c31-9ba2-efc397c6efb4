# دليل النشر للإنتاج

## متطلبات الخادم

### الحد الأدنى للمتطلبات:
- Ubuntu 20.04+ أو CentOS 8+
- Python 3.8+
- PostgreSQL 12+
- Nginx
- 2GB RAM
- 20GB مساحة تخزين

## خطوات النشر

### 1. تحديث النظام
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. تثبيت المتطلبات
```bash
# Python و pip
sudo apt install python3 python3-pip python3-venv -y

# PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Nginx
sudo apt install nginx -y

# Git
sudo apt install git -y
```

### 3. إعداد قاعدة البيانات
```bash
sudo -u postgres psql

CREATE DATABASE codnet_db;
CREATE USER codnet_user WITH PASSWORD 'strong_password';
GRANT ALL PRIVILEGES ON DATABASE codnet_db TO codnet_user;
\q
```

### 4. استنساخ المشروع
```bash
cd /var/www/
sudo git clone <repository-url> codnet
sudo chown -R $USER:$USER /var/www/codnet
cd codnet
```

### 5. إعداد البيئة الافتراضية
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn psycopg2-binary
```

### 6. إعداد متغيرات البيئة
```bash
cp .env.example .env
nano .env
```

تحديث الإعدادات:
```
SECRET_KEY=generate-new-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DATABASE_URL=postgresql://codnet_user:strong_password@localhost:5432/codnet_db
```

### 7. تطبيق الهجرات
```bash
python manage.py makemigrations
python manage.py migrate
python manage.py collectstatic --noinput
python manage.py create_sample_data
python manage.py createsuperuser
```

### 8. إعداد Gunicorn
إنشاء ملف خدمة:
```bash
sudo nano /etc/systemd/system/codnet.service
```

محتوى الملف:
```ini
[Unit]
Description=Codnet Django App
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/codnet
Environment="PATH=/var/www/codnet/venv/bin"
ExecStart=/var/www/codnet/venv/bin/gunicorn --workers 3 --bind unix:/var/www/codnet/codnet.sock codnet_website.wsgi:application

[Install]
WantedBy=multi-user.target
```

تفعيل الخدمة:
```bash
sudo systemctl daemon-reload
sudo systemctl start codnet
sudo systemctl enable codnet
```

### 9. إعداد Nginx
```bash
sudo nano /etc/nginx/sites-available/codnet
```

محتوى الملف:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    location /static/ {
        root /var/www/codnet;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        root /var/www/codnet;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/var/www/codnet/codnet.sock;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

تفعيل الموقع:
```bash
sudo ln -s /etc/nginx/sites-available/codnet /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx
```

### 10. إعداد SSL (Let's Encrypt)
```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 11. إعداد النسخ الاحتياطي
إنشاء سكريبت النسخ الاحتياطي:
```bash
sudo nano /usr/local/bin/backup-codnet.sh
```

محتوى السكريبت:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/codnet"
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -U codnet_user -h localhost codnet_db > $BACKUP_DIR/db_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/codnet/media

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

جعل السكريبت قابل للتنفيذ وإضافته للـ cron:
```bash
sudo chmod +x /usr/local/bin/backup-codnet.sh
sudo crontab -e
```

إضافة السطر التالي للنسخ الاحتياطي اليومي:
```
0 2 * * * /usr/local/bin/backup-codnet.sh
```

## مراقبة الأداء

### مراقبة الخدمات
```bash
# حالة Django
sudo systemctl status codnet

# حالة Nginx
sudo systemctl status nginx

# حالة PostgreSQL
sudo systemctl status postgresql

# مراقبة اللوجز
sudo journalctl -u codnet -f
sudo tail -f /var/log/nginx/error.log
```

### تحسين الأداء
1. تفعيل Redis للكاش
2. تحسين إعدادات PostgreSQL
3. استخدام CDN للملفات الثابتة
4. مراقبة استخدام الذاكرة والمعالج

## الأمان

### تحديثات الأمان
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تحديث Python packages
source /var/www/codnet/venv/bin/activate
pip list --outdated
pip install --upgrade package-name
```

### إعدادات الجدار الناري
```bash
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

## استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ 502**: تحقق من حالة Gunicorn
2. **ملفات CSS لا تظهر**: تشغيل `collectstatic`
3. **خطأ قاعدة البيانات**: تحقق من إعدادات الاتصال
4. **بطء الموقع**: تحقق من استخدام الذاكرة والكاش

### أوامر مفيدة:
```bash
# إعادة تشغيل الخدمات
sudo systemctl restart codnet nginx postgresql

# مراقبة الموارد
htop
df -h
free -m

# تنظيف اللوجز
sudo journalctl --vacuum-time=7d
```
