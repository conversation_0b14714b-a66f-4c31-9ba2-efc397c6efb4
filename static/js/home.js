// ===== HOME PAGE SPECIFIC JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    initHeroAnimations();
    initProjectFilters();
    initServiceHovers();
    initScrollToSection();
});

// ===== SIMPLE HERO EFFECTS =====
function initHeroAnimations() {
    // Simple text display without typing effect
    const typingElement = document.querySelector('.typing-effect');
    if (typingElement) {
        // Just show the text immediately
        typingElement.style.opacity = '1';
    }

    // Simple stats animation on scroll
    const statsSection = document.querySelector('.hero-stats');
    if (statsSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });
        observer.observe(statsSection);
    }
}

// ===== COUNTER ANIMATION =====
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            counter.textContent = Math.floor(current);
            
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    });
}

// ===== SIMPLE PROJECT EFFECTS =====
function initProjectFilters() {
    const projectCards = document.querySelectorAll('.project-card');

    // Simple hover effects
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.2)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
        });
    });
}

// ===== SIMPLE SERVICE HOVERS =====
function initServiceHovers() {
    const serviceCards = document.querySelectorAll('.service-card');

    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Simple hover effect
            const icon = this.querySelector('.service-icon');
            if (icon) {
                icon.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.service-icon');
            if (icon) {
                icon.style.boxShadow = 'none';
            }
        });
    });
}

// ===== SCROLL TO SECTION =====
function initScrollToSection() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            const servicesSection = document.querySelector('#services');
            if (servicesSection) {
                servicesSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }
}

// ===== ADVANCED ANIMATIONS =====

// Stagger animation for cards
function staggerAnimation(elements, delay = 100) {
    elements.forEach((element, index) => {
        setTimeout(() => {
            element.classList.add('animate-in');
        }, index * delay);
    });
}

// Intersection Observer for section animations
const sectionObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const section = entry.target;
            const cards = section.querySelectorAll('.glass-card, .service-card, .project-card, .team-card');
            
            if (cards.length > 0) {
                staggerAnimation(cards, 150);
            }
            
            sectionObserver.unobserve(section);
        }
    });
}, {
    threshold: 0.2,
    rootMargin: '0px 0px -50px 0px'
});

// Observe all sections
document.querySelectorAll('section').forEach(section => {
    sectionObserver.observe(section);
});

// ===== REMOVED PARTICLE EFFECTS FOR BETTER PERFORMANCE =====
// Particle effects removed to improve page loading speed

// ===== SMOOTH REVEAL ANIMATIONS =====
const revealElements = document.querySelectorAll('.fade-in, .slide-up, .slide-left, .slide-right, .scale-in');

const revealObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
            revealObserver.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
});

revealElements.forEach(element => {
    revealObserver.observe(element);
});

// ===== PERFORMANCE OPTIMIZATION =====
let ticking = false;

function requestTick() {
    if (!ticking) {
        requestAnimationFrame(updateAnimations);
        ticking = true;
    }
}

function updateAnimations() {
    // Update scroll-based animations here
    ticking = false;
}

// Throttle scroll events
window.addEventListener('scroll', requestTick);

// ===== PRELOADER =====
window.addEventListener('load', function() {
    // Hide preloader if exists
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        preloader.style.opacity = '0';
        setTimeout(() => {
            preloader.style.display = 'none';
        }, 500);
    }
    
    // Start entrance animations
    setTimeout(() => {
        document.querySelector('.hero-content').classList.add('animate-in');
        document.querySelector('.hero-visual').classList.add('animate-in');
    }, 100);
});
