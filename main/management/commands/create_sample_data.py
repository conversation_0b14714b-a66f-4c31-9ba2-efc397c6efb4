from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from main.models import CompanyInfo, Service, Testimonial
from portfolio.models import Technology, ProjectCategory, Project
from team.models import TeamMember
from contact.models import ContactMessage, ProjectEstimation

class Command(BaseCommand):
    help = 'Create sample data for the website'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create company info
        self.create_company_info()
        
        # Create technologies
        self.create_technologies()
        
        # Create project categories
        self.create_project_categories()
        
        # Create services
        self.create_services()
        
        # Create team members
        self.create_team_members()
        
        # Create projects
        self.create_projects()
        
        # Create testimonials
        self.create_testimonials()
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))

    def create_company_info(self):
        company_info, created = CompanyInfo.objects.get_or_create(
            defaults={
                'name': 'Codnet',
                'tagline': 'نطور أحلامك الرقمية',
                'description': 'شركة مغربية متخصصة في تطوير البرمجيات وتطبيقات الهاتف المحمول ومواقع الويب. نحول أفكارك إلى حلول تقنية مبتكرة.',
                'mission': 'مهمتنا هي تقديم حلول تقنية مبتكرة وعالية الجودة تساعد عملاءنا على تحقيق أهدافهم الرقمية.',
                'vision': 'رؤيتنا أن نكون الشركة الرائدة في تطوير البرمجيات في المغرب والمنطقة العربية.',
                'email': '<EMAIL>',
                'phone': '+212 6XX XXX XXX',
                'address': 'الدار البيضاء، المغرب',
                'linkedin_url': 'https://linkedin.com/company/codnet',
                'facebook_url': 'https://facebook.com/codnet',
                'twitter_url': 'https://twitter.com/codnet',
            }
        )
        if created:
            self.stdout.write('Company info created')

    def create_technologies(self):
        technologies = [
            {'name': 'React', 'icon': 'fab fa-react', 'color': '#61DAFB'},
            {'name': 'Django', 'icon': 'fab fa-python', 'color': '#092E20'},
            {'name': 'Flutter', 'icon': 'fas fa-mobile-alt', 'color': '#02569B'},
            {'name': 'JavaScript', 'icon': 'fab fa-js-square', 'color': '#F7DF1E'},
            {'name': 'Python', 'icon': 'fab fa-python', 'color': '#3776AB'},
            {'name': 'Swift', 'icon': 'fab fa-apple', 'color': '#FA7343'},
            {'name': 'Kotlin', 'icon': 'fab fa-android', 'color': '#0F9D58'},
            {'name': 'Laravel', 'icon': 'fab fa-laravel', 'color': '#FF2D20'},
            {'name': 'Vue.js', 'icon': 'fab fa-vuejs', 'color': '#4FC08D'},
            {'name': 'Node.js', 'icon': 'fab fa-node-js', 'color': '#339933'},
        ]
        
        for tech_data in technologies:
            tech, created = Technology.objects.get_or_create(
                name=tech_data['name'],
                defaults=tech_data
            )
            if created:
                self.stdout.write(f'Technology {tech.name} created')

    def create_project_categories(self):
        categories = [
            {'name': 'تطبيقات الهاتف', 'description': 'تطبيقات iOS و Android'},
            {'name': 'مواقع الويب', 'description': 'مواقع ويب تفاعلية وحديثة'},
            {'name': 'أنظمة الإدارة', 'description': 'أنظمة إدارة مخصصة للشركات'},
            {'name': 'التجارة الإلكترونية', 'description': 'متاجر إلكترونية متكاملة'},
        ]
        
        for cat_data in categories:
            category, created = ProjectCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Category {category.name} created')

    def create_services(self):
        services = [
            {
                'title': 'تطوير تطبيقات الهاتف',
                'description': 'نطور تطبيقات iOS و Android عالية الجودة باستخدام أحدث التقنيات',
                'icon': 'fas fa-mobile-alt',
                'color': '#6366f1',
                'order': 1
            },
            {
                'title': 'تطوير مواقع الويب',
                'description': 'مواقع ويب سريعة ومتجاوبة مع جميع الأجهزة',
                'icon': 'fas fa-globe',
                'color': '#8b5cf6',
                'order': 2
            },
            {
                'title': 'أنظمة الإدارة',
                'description': 'أنظمة إدارة مخصصة لتسهيل عمليات شركتك',
                'icon': 'fas fa-cogs',
                'color': '#06b6d4',
                'order': 3
            },
            {
                'title': 'التجارة الإلكترونية',
                'description': 'متاجر إلكترونية متكاملة مع أنظمة دفع آمنة',
                'icon': 'fas fa-shopping-cart',
                'color': '#10b981',
                'order': 4
            },
            {
                'title': 'تصميم واجهات المستخدم',
                'description': 'تصميمات عصرية وسهلة الاستخدام',
                'icon': 'fas fa-paint-brush',
                'color': '#f59e0b',
                'order': 5
            },
            {
                'title': 'الاستشارات التقنية',
                'description': 'استشارات تقنية متخصصة لمساعدتك في اتخاذ القرارات الصحيحة',
                'icon': 'fas fa-lightbulb',
                'color': '#ef4444',
                'order': 6
            },
        ]
        
        for service_data in services:
            service, created = Service.objects.get_or_create(
                title=service_data['title'],
                defaults=service_data
            )
            if created:
                self.stdout.write(f'Service {service.title} created')

    def create_team_members(self):
        team_members = [
            {
                'name': 'أحمد المهدي',
                'position': 'مطور Full Stack',
                'bio': 'مطور خبير في تقنيات الويب الحديثة مع أكثر من 5 سنوات من الخبرة في تطوير التطبيقات.',
                'skills': 'React, Django, Python, JavaScript, PostgreSQL',
                'experience_years': 5,
                'linkedin_url': 'https://linkedin.com/in/ahmed-mehdi',
                'github_url': 'https://github.com/ahmed-mehdi',
                'order': 1
            },
            {
                'name': 'فاطمة الزهراء',
                'position': 'مطورة تطبيقات الهاتف',
                'bio': 'متخصصة في تطوير تطبيقات iOS و Android باستخدام Flutter و Swift و Kotlin.',
                'skills': 'Flutter, Swift, Kotlin, Firebase, React Native',
                'experience_years': 4,
                'linkedin_url': 'https://linkedin.com/in/fatima-zahra',
                'github_url': 'https://github.com/fatima-zahra',
                'order': 2
            },
            {
                'name': 'يوسف بن علي',
                'position': 'مصمم واجهات المستخدم',
                'bio': 'مصمم مبدع متخصص في تصميم واجهات المستخدم وتجربة المستخدم الحديثة.',
                'skills': 'UI/UX Design, Figma, Adobe XD, Photoshop, Illustrator',
                'experience_years': 3,
                'linkedin_url': 'https://linkedin.com/in/youssef-benali',
                'order': 3
            },
            {
                'name': 'مريم الإدريسي',
                'position': 'مطورة Backend',
                'bio': 'متخصصة في تطوير الخوادم وقواعد البيانات وأنظمة الأمان.',
                'skills': 'Django, Laravel, Node.js, PostgreSQL, MongoDB',
                'experience_years': 4,
                'linkedin_url': 'https://linkedin.com/in/mariam-idrissi',
                'github_url': 'https://github.com/mariam-idrissi',
                'order': 4
            },
        ]
        
        for member_data in team_members:
            member, created = TeamMember.objects.get_or_create(
                name=member_data['name'],
                defaults=member_data
            )
            if created:
                self.stdout.write(f'Team member {member.name} created')

    def create_projects(self):
        # Get some technologies and categories
        react = Technology.objects.filter(name='React').first()
        django = Technology.objects.filter(name='Django').first()
        flutter = Technology.objects.filter(name='Flutter').first()
        
        mobile_category = ProjectCategory.objects.filter(name='تطبيقات الهاتف').first()
        web_category = ProjectCategory.objects.filter(name='مواقع الويب').first()
        
        projects = [
            {
                'title': 'تطبيق إدارة المهام',
                'slug': 'task-management-app',
                'description': 'تطبيق شامل لإدارة المهام والمشاريع مع واجهة سهلة الاستخدام وميزات متقدمة للتعاون.',
                'short_description': 'تطبيق متقدم لإدارة المهام والمشاريع',
                'category': mobile_category,
                'client': 'شركة التقنيات المتقدمة',
                'duration': '3 أشهر',
                'team_size': 3,
                'status': 'completed',
                'featured': True,
                'order': 1
            },
            {
                'title': 'موقع التجارة الإلكترونية',
                'slug': 'ecommerce-website',
                'description': 'متجر إلكتروني متكامل مع نظام دفع آمن وإدارة المخزون والطلبات.',
                'short_description': 'متجر إلكتروني متكامل مع نظام دفع آمن',
                'category': web_category,
                'client': 'متجر الأناقة',
                'duration': '4 أشهر',
                'team_size': 4,
                'status': 'completed',
                'featured': True,
                'order': 2
            },
            {
                'title': 'تطبيق الصحة واللياقة',
                'slug': 'fitness-app',
                'description': 'تطبيق شامل للصحة واللياقة البدنية مع تتبع التمارين والنظام الغذائي.',
                'short_description': 'تطبيق شامل للصحة واللياقة البدنية',
                'category': mobile_category,
                'client': 'نادي اللياقة الذهبي',
                'duration': '5 أشهر',
                'team_size': 3,
                'status': 'completed',
                'featured': True,
                'order': 3
            },
        ]
        
        for project_data in projects:
            project, created = Project.objects.get_or_create(
                title=project_data['title'],
                defaults=project_data
            )
            if created:
                # Add technologies
                if react:
                    project.technologies.add(react)
                if django:
                    project.technologies.add(django)
                if flutter and project.category == mobile_category:
                    project.technologies.add(flutter)
                
                self.stdout.write(f'Project {project.title} created')

    def create_testimonials(self):
        testimonials = [
            {
                'client_name': 'محمد الأحمدي',
                'client_position': 'مدير تقني',
                'client_company': 'شركة التقنيات المتقدمة',
                'testimonial_text': 'فريق Codnet قدم لنا حلولاً تقنية متميزة تجاوزت توقعاتنا. الجودة والاحترافية كانت واضحة في كل تفصيل.',
                'rating': 5,
                'is_featured': True,
                'order': 1
            },
            {
                'client_name': 'سارة المنصوري',
                'client_position': 'مديرة المشاريع',
                'client_company': 'متجر الأناقة',
                'testimonial_text': 'تعاملنا مع Codnet كان تجربة رائعة. التزموا بالمواعيد وقدموا منتجاً يفوق التوقعات.',
                'rating': 5,
                'is_featured': True,
                'order': 2
            },
            {
                'client_name': 'عبد الرحمن الكريم',
                'client_position': 'مؤسس',
                'client_company': 'نادي اللياقة الذهبي',
                'testimonial_text': 'التطبيق الذي طوروه لنا ساعد في زيادة عدد أعضاء النادي بشكل كبير. شكراً لفريق Codnet المتميز.',
                'rating': 5,
                'is_featured': True,
                'order': 3
            },
        ]
        
        for testimonial_data in testimonials:
            testimonial, created = Testimonial.objects.get_or_create(
                client_name=testimonial_data['client_name'],
                defaults=testimonial_data
            )
            if created:
                self.stdout.write(f'Testimonial from {testimonial.client_name} created')
