from django.contrib import admin
from .models import TeamMember

@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    list_display = ['name', 'position', 'experience_years', 'is_active', 'order']
    list_filter = ['is_active', 'position']
    search_fields = ['name', 'position', 'skills']
    list_editable = ['is_active', 'order']

    fieldsets = (
        ('معلومات شخصية', {
            'fields': ('name', 'position', 'photo', 'bio')
        }),
        ('الخبرات والمهارات', {
            'fields': ('skills', 'experience_years')
        }),
        ('روابط التواصل الاجتماعي', {
            'fields': ('linkedin_url', 'github_url', 'twitter_url', 'website_url')
        }),
        ('إعدادات العرض', {
            'fields': ('is_active', 'order')
        }),
    )
