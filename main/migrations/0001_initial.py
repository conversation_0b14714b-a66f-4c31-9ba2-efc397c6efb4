# Generated by Django 5.2.4 on 2025-07-21 23:55

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CompanyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Codnet', max_length=100, verbose_name='اسم الشركة')),
                ('tagline', models.CharField(max_length=200, verbose_name='الشعار')),
                ('description', models.TextField(verbose_name='وصف الشركة')),
                ('mission', models.TextField(verbose_name='المهمة')),
                ('vision', models.TextField(verbose_name='الرؤية')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.Char<PERSON>ield(max_length=20, verbose_name='رقم الهاتف')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('linkedin_url', models.URLField(blank=True, verbose_name='رابط LinkedIn')),
                ('facebook_url', models.URLField(blank=True, verbose_name='رابط Facebook')),
                ('twitter_url', models.URLField(blank=True, verbose_name='رابط Twitter')),
                ('instagram_url', models.URLField(blank=True, verbose_name='رابط Instagram')),
                ('logo', models.ImageField(blank=True, upload_to='branding/', verbose_name='الشعار')),
                ('favicon', models.ImageField(blank=True, upload_to='branding/', verbose_name='الأيقونة المفضلة')),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'معلومات الشركة',
                'verbose_name_plural': 'معلومات الشركة',
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='عنوان الخدمة')),
                ('description', models.TextField(verbose_name='وصف الخدمة')),
                ('icon', models.CharField(help_text='CSS class for icon', max_length=100, verbose_name='أيقونة')),
                ('color', models.CharField(default='#000000', help_text='Hex color code', max_length=7, verbose_name='اللون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'خدمة',
                'verbose_name_plural': 'الخدمات',
                'ordering': ['order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('client_position', models.CharField(max_length=100, verbose_name='منصب العميل')),
                ('client_company', models.CharField(max_length=100, verbose_name='شركة العميل')),
                ('client_photo', models.ImageField(blank=True, upload_to='testimonials/', verbose_name='صورة العميل')),
                ('testimonial_text', models.TextField(verbose_name='نص الشهادة')),
                ('rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], default=5, verbose_name='التقييم')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميزة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'شهادة عميل',
                'verbose_name_plural': 'شهادات العملاء',
                'ordering': ['-is_featured', 'order', '-created_at'],
            },
        ),
    ]
