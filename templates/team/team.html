{% extends 'base.html' %}
{% load static %}

{% block title %}فريق العمل - Codnet{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/team.css' %}">
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title fade-in">فريقنا المتميز</h1>
                <p class="page-subtitle fade-in">
                    تعرف على الخبراء والمبدعين الذين يقفون وراء نجاح مشاريعنا وتحقيق أحلام عملائنا
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Team Stats -->
<section class="team-stats-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card glass-card text-center fade-in" style="--delay: 100ms">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number counter" data-target="{{ team_members.count }}">0</div>
                    <div class="stat-label">عضو فريق</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card glass-card text-center fade-in" style="--delay: 200ms">
                    <div class="stat-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="stat-number counter" data-target="15">0</div>
                    <div class="stat-label">تقنية متقنة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card glass-card text-center fade-in" style="--delay: 300ms">
                    <div class="stat-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <div class="stat-number counter" data-target="50">0</div>
                    <div class="stat-label">مشروع ناجح</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card glass-card text-center fade-in" style="--delay: 400ms">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number counter" data-target="5">0</div>
                    <div class="stat-label">سنوات خبرة</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Members -->
<section class="team-members-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title fade-in">أعضاء الفريق</h2>
                <p class="section-subtitle fade-in">
                    مجموعة من المطورين والمصممين المبدعين الذين يجمعون بين الخبرة والشغف
                </p>
            </div>
        </div>
        
        <div class="row">
            {% for member in team_members %}
            <div class="col-lg-4 col-md-6 mb-5">
                <div class="team-member-card glass-card scale-in" style="--delay: {{ forloop.counter0|add:5 }}00ms">
                    <div class="member-image-container">
                        <div class="member-image">
                            {% if member.photo %}
                            <img src="{{ member.photo.url }}" alt="{{ member.name }}">
                            {% else %}
                            <div class="placeholder-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="member-overlay">
                            <div class="member-social">
                                {% if member.linkedin_url %}
                                <a href="{{ member.linkedin_url }}" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                {% endif %}
                                {% if member.github_url %}
                                <a href="{{ member.github_url }}" target="_blank" class="social-link">
                                    <i class="fab fa-github"></i>
                                </a>
                                {% endif %}
                                {% if member.twitter_url %}
                                <a href="{{ member.twitter_url }}" target="_blank" class="social-link">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                {% endif %}
                                {% if member.website_url %}
                                <a href="{{ member.website_url }}" target="_blank" class="social-link">
                                    <i class="fas fa-globe"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="member-content">
                        <h3 class="member-name">{{ member.name }}</h3>
                        <p class="member-position">{{ member.position }}</p>
                        
                        <div class="member-experience">
                            <i class="fas fa-calendar-alt"></i>
                            <span>{{ member.experience_years }} سنوات خبرة</span>
                        </div>
                        
                        <p class="member-bio">{{ member.bio|truncatewords:20 }}</p>
                        
                        <div class="member-skills">
                            <h5>المهارات:</h5>
                            <div class="skills-list">
                                {% for skill in member.get_skills_list|slice:":5" %}
                                <span class="skill-tag">{{ skill }}</span>
                                {% endfor %}
                                {% if member.get_skills_list|length > 5 %}
                                <span class="skill-more">+{{ member.get_skills_list|length|add:"-5" }}</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <button class="btn-view-more" data-member="{{ forloop.counter0 }}">
                            <i class="fas fa-plus"></i>
                            المزيد
                        </button>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="no-team glass-card text-center">
                    <i class="fas fa-users"></i>
                    <h3>لا يوجد أعضاء فريق</h3>
                    <p>سيتم إضافة أعضاء الفريق قريباً.</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Team Values -->
<section class="team-values-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title fade-in">قيمنا وثقافتنا</h2>
                <p class="section-subtitle fade-in">
                    المبادئ التي نؤمن بها ونعمل وفقاً لها في كل مشروع
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 100ms">
                    <div class="value-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h4 class="value-title">الإبداع والابتكار</h4>
                    <p class="value-description">
                        نسعى دائماً لإيجاد حلول مبتكرة وإبداعية تتجاوز توقعات عملائنا
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 200ms">
                    <div class="value-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h4 class="value-title">التعاون والعمل الجماعي</h4>
                    <p class="value-description">
                        نؤمن بقوة العمل الجماعي وأن النجاح يتحقق من خلال التعاون والتكامل
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 300ms">
                    <div class="value-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4 class="value-title">الجودة والتميز</h4>
                    <p class="value-description">
                        نلتزم بأعلى معايير الجودة في كل ما نقوم به لضمان تحقيق أفضل النتائج
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 400ms">
                    <div class="value-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h4 class="value-title">التعلم المستمر</h4>
                    <p class="value-description">
                        نحرص على مواكبة أحدث التقنيات والتطورات في عالم البرمجة والتكنولوجيا
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 500ms">
                    <div class="value-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h4 class="value-title">الشغف والحماس</h4>
                    <p class="value-description">
                        نعمل بشغف وحماس كبيرين لأننا نحب ما نقوم به ونؤمن برسالتنا
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="value-card glass-card text-center slide-up" style="--delay: 600ms">
                    <div class="value-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="value-title">الالتزام بالمواعيد</h4>
                    <p class="value-description">
                        نحترم الوقت ونلتزم بالمواعيد المحددة لتسليم المشاريع في الوقت المناسب
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Join Team CTA -->
<section class="join-team-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <div class="join-team-content glass-card fade-in">
                    <h2 class="join-title">انضم إلى فريقنا</h2>
                    <p class="join-description">
                        هل تريد أن تكون جزءاً من فريق متميز ومبدع؟ نحن نبحث دائماً عن المواهب الجديدة
                    </p>
                    <div class="join-buttons">
                        <a href="{% url 'contact:contact' %}" class="btn-primary-custom">
                            <i class="fas fa-paper-plane"></i>
                            أرسل سيرتك الذاتية
                        </a>
                        <a href="{% url 'contact:contact' %}" class="btn-secondary-custom">
                            <i class="fas fa-phone"></i>
                            تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Member Modal -->
<div class="modal fade" id="memberModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content glass-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="memberModalLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="memberModalBody">
                <!-- Member details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/team.js' %}"></script>
<script>
    // Pass team members data to JavaScript
    const teamMembersData = [
        {% for member in team_members %}
        {
            name: "{{ member.name|escapejs }}",
            position: "{{ member.position|escapejs }}",
            bio: "{{ member.bio|escapejs }}",
            experience_years: {{ member.experience_years }},
            skills: [{% for skill in member.get_skills_list %}"{{ skill|escapejs }}"{% if not forloop.last %},{% endif %}{% endfor %}],
            photo: "{% if member.photo %}{{ member.photo.url }}{% endif %}",
            linkedin_url: "{{ member.linkedin_url|default:'' }}",
            github_url: "{{ member.github_url|default:'' }}",
            twitter_url: "{{ member.twitter_url|default:'' }}",
            website_url: "{{ member.website_url|default:'' }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
</script>
{% endblock %}
