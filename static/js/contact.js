// ===== CONTACT PAGE JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    initFormAnimations();
    initFileUpload();
    initFormValidation();
    initContactAnimations();
    initMap();
});

// ===== FORM ANIMATIONS =====
function initFormAnimations() {
    const formGroups = document.querySelectorAll('.form-group');
    
    formGroups.forEach(group => {
        const input = group.querySelector('input, textarea, select');
        const label = group.querySelector('label');
        
        if (input && label) {
            // Check if input has value on load
            if (input.value) {
                label.classList.add('float');
            }
            
            // Float label on focus
            input.addEventListener('focus', () => {
                label.classList.add('float');
                group.classList.add('focused');
            });
            
            // Remove float if empty on blur
            input.addEventListener('blur', () => {
                if (!input.value) {
                    label.classList.remove('float');
                }
                group.classList.remove('focused');
            });
            
            // Float label on input
            input.addEventListener('input', () => {
                if (input.value) {
                    label.classList.add('float');
                } else {
                    label.classList.remove('float');
                }
            });
        }
    });
}

// ===== FILE UPLOAD =====
function initFileUpload() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = fileUploadArea?.querySelector('input[type="file"]');
    
    if (!fileUploadArea || !fileInput) return;
    
    // Drag and drop events
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.style.borderColor = 'var(--primary-color)';
        this.style.background = 'rgba(99, 102, 241, 0.1)';
    });
    
    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.style.background = 'transparent';
    });
    
    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        this.style.background = 'transparent';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateFileDisplay(files[0]);
        }
    });
    
    // File input change
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            updateFileDisplay(this.files[0]);
        }
    });
    
    function updateFileDisplay(file) {
        const content = fileUploadArea.querySelector('.file-upload-content');
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        
        content.innerHTML = `
            <i class="fas fa-file-check"></i>
            <p><strong>${fileName}</strong></p>
            <small>${fileSize} MB</small>
        `;
        
        fileUploadArea.style.borderColor = 'var(--success-color)';
        fileUploadArea.style.background = 'rgba(16, 185, 129, 0.1)';
    }
}

// ===== FORM VALIDATION =====
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('.btn-submit');
            if (submitBtn) {
                showLoadingState(submitBtn);
            }
        });
    });
}

function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        const formGroup = field.closest('.form-group');
        const errorElement = formGroup?.querySelector('.form-error');
        
        // Remove existing error styling
        field.style.borderColor = '';
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        
        // Validate field
        if (!field.value.trim()) {
            isValid = false;
            field.style.borderColor = 'var(--danger-color)';
            
            if (errorElement) {
                errorElement.textContent = 'هذا الحقل مطلوب';
                errorElement.style.display = 'block';
            }
        } else if (field.type === 'email' && !isValidEmail(field.value)) {
            isValid = false;
            field.style.borderColor = 'var(--danger-color)';
            
            if (errorElement) {
                errorElement.textContent = 'يرجى إدخال بريد إلكتروني صحيح';
                errorElement.style.display = 'block';
            }
        }
    });
    
    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showLoadingState(button) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (btnText && btnLoading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        button.disabled = true;
    }
}

function hideLoadingState(button) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (btnText && btnLoading) {
        btnText.style.display = 'inline-flex';
        btnLoading.style.display = 'none';
        button.disabled = false;
    }
}

// ===== CONTACT ANIMATIONS =====
function initContactAnimations() {
    // Contact cards hover effects
    const contactCards = document.querySelectorAll('.contact-card');
    
    contactCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.contact-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
                icon.style.boxShadow = '0 10px 25px rgba(99, 102, 241, 0.4)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.contact-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
                icon.style.boxShadow = 'none';
            }
        });
    });
    
    // Process steps animation
    const processSteps = document.querySelectorAll('.process-step');
    
    processSteps.forEach((step, index) => {
        step.addEventListener('mouseenter', function() {
            const stepIcon = this.querySelector('.step-icon');
            if (stepIcon) {
                stepIcon.style.transform = 'scale(1.1)';
                stepIcon.style.boxShadow = '0 10px 25px rgba(6, 182, 212, 0.4)';
            }
        });
        
        step.addEventListener('mouseleave', function() {
            const stepIcon = this.querySelector('.step-icon');
            if (stepIcon) {
                stepIcon.style.transform = 'scale(1)';
                stepIcon.style.boxShadow = 'none';
            }
        });
    });
    
    // Feature cards animation
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(-5deg)';
                icon.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.4)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
                icon.style.boxShadow = 'none';
            }
        });
    });
    
    // Social links animation
    const socialLinks = document.querySelectorAll('.social-link-large');
    
    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1.2) rotate(10deg)';
            }
        });
        
        link.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
}

// ===== MAP INITIALIZATION =====
function initMap() {
    // Placeholder for Google Maps integration
    const mapElement = document.getElementById('map');
    
    if (mapElement) {
        // This would be replaced with actual Google Maps initialization
        mapElement.addEventListener('click', function() {
            // Open Google Maps in new tab
            window.open('https://maps.google.com/?q=Casablanca,Morocco', '_blank');
        });
        
        mapElement.style.cursor = 'pointer';
    }
}

// ===== AJAX FORM SUBMISSION =====
function initAjaxForms() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm(this)) {
                return;
            }
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.btn-submit');
            
            showLoadingState(submitBtn);
            
            fetch('/contact/ajax/contact/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingState(submitBtn);
                
                if (data.success) {
                    showSuccessMessage(data.message);
                    this.reset();
                    
                    // Reset form labels
                    const labels = this.querySelectorAll('label.float');
                    labels.forEach(label => label.classList.remove('float'));
                } else {
                    showErrorMessage(data.message);
                }
            })
            .catch(error => {
                hideLoadingState(submitBtn);
                showErrorMessage('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
                console.error('Error:', error);
            });
        });
    }
}

function showSuccessMessage(message) {
    showAlert(message, 'success');
}

function showErrorMessage(message) {
    showAlert(message, 'danger');
}

function showAlert(message, type) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const form = document.querySelector('form');
    if (form) {
        form.insertAdjacentHTML('afterbegin', alertHTML);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = form.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    const elements = document.querySelectorAll('.fade-in, .slide-up, .scale-in');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    elements.forEach(element => {
        observer.observe(element);
    });
}

// Initialize scroll animations
initScrollAnimations();

// Initialize AJAX forms (optional)
// initAjaxForms();

// ===== ACCESSIBILITY IMPROVEMENTS =====
function initAccessibility() {
    // Add keyboard navigation for interactive elements
    const interactiveElements = document.querySelectorAll('.contact-card, .process-step, .feature-card');
    
    interactiveElements.forEach(element => {
        element.setAttribute('tabindex', '0');
        
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // Add ARIA labels for form elements
    const formInputs = document.querySelectorAll('input, textarea, select');
    formInputs.forEach(input => {
        const label = input.nextElementSibling;
        if (label && label.tagName === 'LABEL') {
            input.setAttribute('aria-label', label.textContent);
        }
    });
}

// Initialize accessibility features
initAccessibility();

// ===== PERFORMANCE OPTIMIZATION =====
let ticking = false;

function requestTick() {
    if (!ticking) {
        requestAnimationFrame(updateAnimations);
        ticking = true;
    }
}

function updateAnimations() {
    // Update scroll-based animations here
    ticking = false;
}

// Throttle scroll events
window.addEventListener('scroll', requestTick);
