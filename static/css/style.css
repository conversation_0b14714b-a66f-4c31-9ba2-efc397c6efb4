/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #2d3748;
    --secondary-color: #4a5568;
    --accent-color: #718096;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --dark-color: #1a202c;
    --light-color: #f7fafc;
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-light: #f7fafc;
    --bg-dark: #1a202c;
    --bg-soft: #2d3748;
    --bg-light: #f7fafc;
    --gradient-primary: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    --gradient-secondary: linear-gradient(135deg, #4a5568 0%, #718096 100%);
    --gradient-accent: linear-gradient(135deg, #718096 0%, #a0aec0 100%);
    --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: var(--bg-dark);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== SIMPLE BACKGROUND ===== */
.abstract-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--bg-dark);
}

/* Remove floating shapes for better performance */

/* ===== NAVIGATION ===== */
.glass-nav {
    background: var(--bg-soft);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
}

.glass-nav.scrolled {
    background: var(--bg-dark);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-shape {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.logo-shape::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--light-color);
    border-radius: 4px;
    transform: translate(-50%, -50%) rotate(45deg);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--text-light) !important;
    background: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    background: var(--gradient-primary);
    color: var(--text-light) !important;
}

/* ===== BUTTONS ===== */
.btn-primary-custom {
    background: var(--gradient-primary);
    border: none;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
}

.btn-primary-custom:hover {
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-heavy);
    color: var(--text-light);
}

.btn-secondary-custom {
    background: transparent;
    border: 2px solid var(--text-light);
    padding: 12px 30px;
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
}

.btn-secondary-custom:hover {
    background: var(--text-light);
    color: var(--primary-color);
}

/* ===== CARDS ===== */
.glass-card {
    background: var(--bg-soft);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.glass-card:hover {
    box-shadow: var(--shadow-medium);
    background: rgba(74, 85, 104, 0.8);
}

/* ===== SECTIONS ===== */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.section-subtitle {
    font-size: 1.1rem;
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== FOOTER ===== */
.footer-section {
    background: var(--bg-dark);
    color: var(--text-light);
    padding: 60px 0 20px;
    margin-top: 80px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand .logo-text {
    font-size: 2rem;
}

.footer-description {
    color: var(--text-secondary);
    margin-top: 1rem;
    line-height: 1.6;
}

.footer-title {
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--text-light);
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.contact-info i {
    color: var(--text-light);
    margin-left: 10px;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    box-shadow: var(--shadow-medium);
    color: var(--text-light);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.copyright {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== HOME PAGE STYLES ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-title {
    font-size: 3rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.gradient-text {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.counter {
    display: block;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 400px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.code-block {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-soft);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    min-width: 300px;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.code-header {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot.red { background: #ff5f56; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #27ca3f; }

.code-content {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
}

.code-line {
    margin-bottom: 0.5rem;
}

.keyword { color: #ff79c6; }
.function { color: #50fa7b; }
.string { color: #f1fa8c; }

.tech-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.tech-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.2rem;
    transition: var(--transition);
}

.tech-icon:nth-child(2) { top: 10%; right: 20%; }
.tech-icon:nth-child(3) { top: 30%; left: 10%; }
.tech-icon:nth-child(4) { bottom: 30%; right: 10%; }
.tech-icon:nth-child(5) { bottom: 10%; left: 30%; }
.tech-icon:nth-child(6) { top: 50%; right: 5%; }

.tech-icon:hover {
    transform: scale(1.1);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-arrow {
    color: var(--text-secondary);
    font-size: 1.5rem;
    transition: var(--transition);
}

.scroll-arrow:hover {
    color: var(--text-light);
}

/* Service Cards */
.service-card {
    text-align: center;
    padding: 2rem;
    height: 100%;
    transition: var(--transition);
}

.service-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: var(--text-light);
}

.service-title {
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Project Cards */
.project-card {
    overflow: hidden;
    padding: 0;
    height: 100%;
    transition: var(--transition);

}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 3rem;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-link {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 1.2rem;
    transition: var(--transition);
}

.project-link:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
    color: var(--text-light);
}

.project-content {
    padding: 1.5rem;
}

.project-title {
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Team Cards */
.team-card {
    text-align: center;
    padding: 2rem 1.5rem;
    height: 100%;

}

.team-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-avatar {
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.team-name {
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-position {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.team-social {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.team-social a {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.team-social a:hover {
    background: var(--primary-color);

    color: white;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.cta-content {
    text-align: center;
    padding: 4rem 3rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .logo-text {
        font-size: 1.2rem !important;
    }

    .floating-shape {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .tech-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .code-block {
        min-width: 250px;
        font-size: 0.8rem;
    }
}
