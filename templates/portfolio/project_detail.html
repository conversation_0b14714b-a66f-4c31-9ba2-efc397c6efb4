{% extends 'base.html' %}
{% load static %}

{% block title %}{{ project.title }} - Codnet{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/portfolio.css' %}">
<link rel="stylesheet" href="{% static 'css/lightbox.css' %}">
{% endblock %}

{% block content %}
<!-- Project Hero -->
<section class="project-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="project-info fade-in">
                    <div class="project-breadcrumb">
                        <a href="{% url 'portfolio:projects' %}">المشاريع</a>
                        <span>/</span>
                        <span>{{ project.title }}</span>
                    </div>
                    
                    <h1 class="project-title">{{ project.title }}</h1>
                    
                    <div class="project-meta">
                        <span class="meta-item">
                            <i class="fas fa-folder"></i>
                            {{ project.category.name }}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            {{ project.created_at|date:"Y" }}
                        </span>
                        {% if project.client %}
                        <span class="meta-item">
                            <i class="fas fa-user"></i>
                            {{ project.client }}
                        </span>
                        {% endif %}
                        {% if project.duration %}
                        <span class="meta-item">
                            <i class="fas fa-clock"></i>
                            {{ project.duration }}
                        </span>
                        {% endif %}
                    </div>
                    
                    <p class="project-description">{{ project.description|linebreaks }}</p>
                    
                    <div class="project-technologies">
                        <h4>التقنيات المستخدمة:</h4>
                        <div class="tech-list">
                            {% for tech in project.technologies.all %}
                            <span class="tech-tag" style="background-color: {{ tech.color }}20; color: {{ tech.color }}">
                                {% if tech.icon %}<i class="{{ tech.icon }}"></i>{% endif %}
                                {{ tech.name }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="project-links">
                        {% if project.live_url %}
                        <a href="{{ project.live_url }}" target="_blank" class="btn-primary-custom">
                            <i class="fas fa-external-link-alt"></i>
                            زيارة المشروع
                        </a>
                        {% endif %}
                        {% if project.github_url %}
                        <a href="{{ project.github_url }}" target="_blank" class="btn-secondary-custom">
                            <i class="fab fa-github"></i>
                            كود المصدر
                        </a>
                        {% endif %}
                        {% if project.app_store_url %}
                        <a href="{{ project.app_store_url }}" target="_blank" class="btn-secondary-custom">
                            <i class="fab fa-apple"></i>
                            App Store
                        </a>
                        {% endif %}
                        {% if project.play_store_url %}
                        <a href="{{ project.play_store_url }}" target="_blank" class="btn-secondary-custom">
                            <i class="fab fa-google-play"></i>
                            Play Store
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="project-featured-image slide-left">
                    {% if project.featured_image %}
                    <img src="{{ project.featured_image.url }}" alt="{{ project.title }}" 
                         class="main-image" data-lightbox="project-gallery">
                    {% else %}
                    <div class="placeholder-image">
                        <i class="fas fa-image"></i>
                        <p>لا توجد صورة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Gallery -->
{% if project.get_gallery_images %}
<section class="project-gallery section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title fade-in">معرض الصور</h2>
            </div>
        </div>
        
        <div class="row">
            {% for image in project.get_gallery_images %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="gallery-item scale-in" style="--delay: {{ forloop.counter0|add:1 }}00ms">
                    <img src="{{ image.url }}" alt="{{ project.title }} - صورة {{ forloop.counter }}" 
                         data-lightbox="project-gallery">
                    <div class="gallery-overlay">
                        <i class="fas fa-search-plus"></i>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Project Details -->
<section class="project-details section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="project-content-section glass-card fade-in">
                    <h3>تفاصيل المشروع</h3>
                    <div class="content-text">
                        {{ project.description|linebreaks }}
                    </div>
                    
                    {% if project.team_size > 1 %}
                    <div class="project-stats">
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <span class="stat-number">{{ project.team_size }}</span>
                            <span class="stat-label">أعضاء الفريق</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <div class="sidebar-card glass-card fade-in">
                        <h4>معلومات المشروع</h4>
                        <ul class="project-info-list">
                            <li>
                                <strong>الفئة:</strong>
                                <span>{{ project.category.name }}</span>
                            </li>
                            <li>
                                <strong>الحالة:</strong>
                                <span class="status-{{ project.status }}">
                                    {% if project.status == 'completed' %}مكتمل
                                    {% elif project.status == 'in_progress' %}قيد التطوير
                                    {% else %}مخطط{% endif %}
                                </span>
                            </li>
                            {% if project.client %}
                            <li>
                                <strong>العميل:</strong>
                                <span>{{ project.client }}</span>
                            </li>
                            {% endif %}
                            {% if project.duration %}
                            <li>
                                <strong>مدة التطوير:</strong>
                                <span>{{ project.duration }}</span>
                            </li>
                            {% endif %}
                            <li>
                                <strong>تاريخ الإنشاء:</strong>
                                <span>{{ project.created_at|date:"F Y" }}</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-card glass-card fade-in">
                        <h4>شارك المشروع</h4>
                        <div class="share-buttons">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" 
                               target="_blank" class="share-btn facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ project.title }}" 
                               target="_blank" class="share-btn twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}" 
                               target="_blank" class="share-btn linkedin">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="whatsapp://send?text={{ project.title }} {{ request.build_absolute_uri }}" 
                               class="share-btn whatsapp">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
{% if related_projects %}
<section class="related-projects section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title fade-in">مشاريع ذات صلة</h2>
            </div>
        </div>
        
        <div class="row">
            {% for related in related_projects %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="project-card glass-card scale-in" style="--delay: {{ forloop.counter0|add:2 }}00ms">
                    <div class="project-image">
                        {% if related.featured_image %}
                        <img src="{{ related.featured_image.url }}" alt="{{ related.title }}">
                        {% else %}
                        <div class="placeholder-image">
                            <i class="fas fa-image"></i>
                        </div>
                        {% endif %}
                        <div class="project-overlay">
                            <a href="{{ related.get_absolute_url }}" class="project-link">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    <div class="project-content">
                        <h5 class="project-title">
                            <a href="{{ related.get_absolute_url }}">{{ related.title }}</a>
                        </h5>
                        <p class="project-description">{{ related.short_description }}</p>
                        <div class="project-tech">
                            {% for tech in related.technologies.all|slice:":3" %}
                            <span class="tech-tag">{{ tech.name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- CTA Section -->
<section class="cta-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <div class="cta-content glass-card fade-in">
                    <h2 class="cta-title">أعجبك هذا المشروع؟</h2>
                    <p class="cta-description">
                        دعنا نساعدك في إنشاء مشروع مماثل أو أفضل لعملك
                    </p>
                    <div class="cta-buttons">
                        <a href="{% url 'contact:estimation' %}" class="btn-primary-custom">
                            <i class="fas fa-calculator"></i>
                            احصل على تقدير
                        </a>
                        <a href="{% url 'contact:contact' %}" class="btn-secondary-custom">
                            <i class="fas fa-phone"></i>
                            تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/lightbox.js' %}"></script>
<script src="{% static 'js/portfolio.js' %}"></script>
{% endblock %}
