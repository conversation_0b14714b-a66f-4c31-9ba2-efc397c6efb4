# موقع Codnet - شركة تطوير البرمجيات

موقع تعريفي احترافي لشركة Codnet المغربية المتخصصة في تطوير البرمجيات وتطبيقات الهاتف المحمول.

## المميزات

### 🎨 التصميم
- تصميم مجرد فريد من نوعه
- أنيميشن جذاب باستخدام JavaScript
- واجهة مستخدم متجاوبة مع جميع الأجهزة
- ألوان وتدرجات عصرية

### 🚀 التقنيات المستخدمة
- **Backend**: Django 5.2 + Python
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Database**: SQLite (للتطوير) / PostgreSQL (للإنتاج)
- **Styling**: Bootstrap 5 + CSS مخصص
- **Icons**: Font Awesome 6

### 📱 الصفحات والميزات
- **الصفحة الرئيسية**: عرض الشركة والخدمات والمشاريع المميزة
- **صفحة المشاريع**: عرض جميع المشاريع مع إمكانية التصفية والبحث
- **صفحة الفريق**: عرض أعضاء الفريق مع معلومات تفصيلية
- **تقدير المشروع**: نموذج تفاعلي لطلب تقدير المشاريع
- **صفحة الاتصال**: نموذج اتصال وخريطة ومعلومات التواصل

### ⚡ تحسينات الأداء والـ SEO
- Sitemap.xml تلقائي
- Robots.txt محسن
- Structured Data (JSON-LD)
- Meta tags محسنة
- Lazy loading للصور
- Preload للموارد المهمة
- PWA manifest

## متطلبات التشغيل

- Python 3.8+
- Django 5.2+
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd "codnet website"
```

### 2. إنشاء البيئة الافتراضية
```bash
python3 -m venv codnet_env
source codnet_env/bin/activate  # على macOS/Linux
# أو
codnet_env\Scripts\activate  # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install django pillow requests python-decouple
```

### 4. تطبيق الهجرات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء مستخدم إداري
```bash
python manage.py createsuperuser
```

### 6. إنشاء البيانات التجريبية
```bash
python manage.py create_sample_data
```

### 7. تشغيل الخادم
```bash
python manage.py runserver
```

الموقع سيكون متاحاً على: http://127.0.0.1:8000

## لوحة الإدارة

يمكن الوصول للوحة الإدارة عبر: http://127.0.0.1:8000/admin

### الميزات الإدارية:
- إدارة معلومات الشركة
- إدارة المشاريع والتقنيات
- إدارة أعضاء الفريق
- إدارة الخدمات والشهادات
- عرض رسائل الاتصال وطلبات التقدير

## هيكل المشروع

```
codnet website/
├── codnet_website/          # إعدادات Django الرئيسية
├── main/                    # التطبيق الرئيسي
├── portfolio/               # تطبيق المشاريع
├── team/                    # تطبيق الفريق
├── contact/                 # تطبيق الاتصال
├── templates/               # قوالب HTML
├── static/                  # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── images/             # الصور
├── media/                   # ملفات المستخدمين المرفوعة
└── requirements.txt         # متطلبات Python
```

## التخصيص

### إضافة مشروع جديد
1. ادخل للوحة الإدارة
2. اذهب إلى "المشاريع" > "إضافة مشروع"
3. املأ البيانات المطلوبة
4. ارفع الصور
5. احفظ المشروع

### إضافة عضو فريق جديد
1. ادخل للوحة الإدارة
2. اذهب إلى "أعضاء الفريق" > "إضافة عضو فريق"
3. املأ البيانات والمهارات
4. ارفع الصورة الشخصية
5. احفظ البيانات

### تخصيص الألوان والتصميم
يمكن تعديل الألوان والتصميم من خلال ملف `static/css/style.css`:

```css
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    /* ... باقي المتغيرات */
}
```

## دمج Google Sheets

لدمج نموذج تقدير المشاريع مع Google Sheets:

1. أنشئ Google Sheet جديد
2. احصل على API credentials
3. حدث دالة `send_to_google_sheets` في `contact/views.py`
4. أضف URL الـ Sheet في الإعدادات

## النشر للإنتاج

### إعدادات الإنتاج
1. حدث `DEBUG = False` في settings.py
2. أضف ALLOWED_HOSTS
3. استخدم قاعدة بيانات PostgreSQL
4. اضبط إعدادات البريد الإلكتروني
5. استخدم خادم ويب مثل Nginx + Gunicorn

### متغيرات البيئة
أنشئ ملف `.env` للإعدادات الحساسة:
```
SECRET_KEY=your-secret-key
DEBUG=False
DATABASE_URL=postgresql://...
EMAIL_HOST_USER=your-email
EMAIL_HOST_PASSWORD=your-password
```

## الدعم والمساهمة

للحصول على الدعم أو المساهمة في المشروع:
- البريد الإلكتروني: <EMAIL>
- الموقع: https://codnet.ma

## الترخيص

هذا المشروع مطور خصيصاً لشركة Codnet.

---

تم تطوير هذا الموقع باستخدام أفضل الممارسات في تطوير الويب مع التركيز على الأداء وتجربة المستخدم والـ SEO.
