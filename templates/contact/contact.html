{% extends 'base.html' %}
{% load static %}

{% block title %}اتصل بنا - Codnet{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/contact.css' %}">
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title fade-in">اتصل بنا</h1>
                <p class="page-subtitle fade-in">
                    نحن هنا لمساعدتك في تحويل أفكارك إلى واقع رقمي مذهل
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="contact-info-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card glass-card text-center slide-up" style="--delay: 100ms">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4 class="contact-title">العنوان</h4>
                    <p class="contact-details">
                        المغرب<br>
                        الدار البيضاء
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card glass-card text-center slide-up" style="--delay: 200ms">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4 class="contact-title">الهاتف</h4>
                    <p class="contact-details">
                        <a href="tel:+212600000000">+212 6XX XXX XXX</a><br>
                        <a href="tel:+212500000000">+212 5XX XXX XXX</a>
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="contact-card glass-card text-center slide-up" style="--delay: 300ms">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4 class="contact-title">البريد الإلكتروني</h4>
                    <p class="contact-details">
                        <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form and Map -->
<section class="contact-form-section section-padding">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-6 mb-5">
                <div class="form-container glass-card fade-in">
                    <div class="form-header">
                        <h3 class="form-title">أرسل لنا رسالة</h3>
                        <p class="form-subtitle">
                            نحن نحب أن نسمع منك. أرسل لنا رسالة وسنرد عليك في أقرب وقت ممكن.
                        </p>
                    </div>
                    
                    {% if messages %}
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" id="contactForm" class="contact-form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name }}
                                    <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                                    {% if form.name.errors %}
                                        <div class="form-error">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.email }}
                                    <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                                    {% if form.email.errors %}
                                        <div class="form-error">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.phone }}
                                    <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                                    {% if form.phone.errors %}
                                        <div class="form-error">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.subject }}
                                    <label for="{{ form.subject.id_for_label }}">{{ form.subject.label }}</label>
                                    {% if form.subject.errors %}
                                        <div class="form-error">{{ form.subject.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            {{ form.message }}
                            <label for="{{ form.message.id_for_label }}">{{ form.message.label }}</label>
                            {% if form.message.errors %}
                                <div class="form-error">{{ form.message.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-submit">
                            <button type="submit" class="btn-primary-custom btn-submit">
                                <i class="fas fa-paper-plane"></i>
                                <span class="btn-text">إرسال الرسالة</span>
                                <span class="btn-loading" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري الإرسال...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Map and Additional Info -->
            <div class="col-lg-6">
                <div class="map-container glass-card fade-in">
                    <div class="map-header">
                        <h3 class="map-title">موقعنا</h3>
                        <p class="map-subtitle">تفضل بزيارتنا في مكتبنا</p>
                    </div>
                    
                    <!-- Google Map Placeholder -->
                    <div class="map-placeholder" id="map">
                        <div class="map-content">
                            <i class="fas fa-map-marked-alt"></i>
                            <h4>خريطة تفاعلية</h4>
                            <p>سيتم إضافة الخريطة التفاعلية هنا</p>
                        </div>
                    </div>
                    
                    <!-- Business Hours -->
                    <div class="business-hours">
                        <h4 class="hours-title">ساعات العمل</h4>
                        <div class="hours-list">
                            <div class="hours-item">
                                <span class="day">الاثنين - الجمعة</span>
                                <span class="time">9:00 ص - 6:00 م</span>
                            </div>
                            <div class="hours-item">
                                <span class="day">السبت</span>
                                <span class="time">10:00 ص - 4:00 م</span>
                            </div>
                            <div class="hours-item">
                                <span class="day">الأحد</span>
                                <span class="time">مغلق</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Social Media -->
<section class="social-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="section-title fade-in">تابعنا على وسائل التواصل</h2>
                <p class="section-subtitle fade-in">
                    ابق على اطلاع بآخر أخبارنا ومشاريعنا
                </p>
                
                <div class="social-links-large">
                    <a href="#" class="social-link-large scale-in" style="--delay: 100ms">
                        <i class="fab fa-linkedin-in"></i>
                        <span>LinkedIn</span>
                    </a>
                    <a href="#" class="social-link-large scale-in" style="--delay: 200ms">
                        <i class="fab fa-facebook-f"></i>
                        <span>Facebook</span>
                    </a>
                    <a href="#" class="social-link-large scale-in" style="--delay: 300ms">
                        <i class="fab fa-twitter"></i>
                        <span>Twitter</span>
                    </a>
                    <a href="#" class="social-link-large scale-in" style="--delay: 400ms">
                        <i class="fab fa-instagram"></i>
                        <span>Instagram</span>
                    </a>
                    <a href="#" class="social-link-large scale-in" style="--delay: 500ms">
                        <i class="fab fa-github"></i>
                        <span>GitHub</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title fade-in">الأسئلة الشائعة</h2>
                <p class="section-subtitle fade-in">
                    إجابات على أكثر الأسئلة شيوعاً
                </p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item glass-card fade-in">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                كم من الوقت يستغرق تطوير التطبيق؟
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                يعتمد الوقت على تعقيد المشروع وحجمه. عادة ما يستغرق تطوير التطبيق البسيط من 2-4 أشهر، بينما التطبيقات المعقدة قد تستغرق 6-12 شهر.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item glass-card fade-in">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                ما هي تكلفة تطوير موقع ويب؟
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                تختلف التكلفة حسب نوع الموقع والميزات المطلوبة. نقدم تقديراً مجانياً مفصلاً بعد دراسة متطلباتك.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item glass-card fade-in">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                هل تقدمون خدمات الصيانة والدعم؟
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، نقدم خدمات الصيانة والدعم الفني لجميع مشاريعنا مع ضمان لمدة سنة كاملة.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item glass-card fade-in">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                ما هي التقنيات التي تستخدمونها؟
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نستخدم أحدث التقنيات مثل React، Django، Flutter، Swift، Kotlin وغيرها حسب متطلبات المشروع.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <div class="cta-content glass-card fade-in">
                    <h2 class="cta-title">مستعد لبدء مشروعك؟</h2>
                    <p class="cta-description">
                        دعنا نساعدك في تحويل فكرتك إلى واقع رقمي مذهل
                    </p>
                    <div class="cta-buttons">
                        <a href="{% url 'contact:estimation' %}" class="btn-primary-custom">
                            <i class="fas fa-calculator"></i>
                            احصل على تقدير مجاني
                        </a>
                        <a href="tel:+212600000000" class="btn-secondary-custom">
                            <i class="fas fa-phone"></i>
                            اتصل بنا الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/contact.js' %}"></script>
{% endblock %}
