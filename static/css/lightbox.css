/* ===== LIGHTBOX STYLES ===== */

.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox-overlay.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-container {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.lightbox-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.lightbox-prev {
    left: -80px;
}

.lightbox-next {
    right: -80px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-counter {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

/* Gallery Items */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    animation-delay: var(--delay);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Project Detail Styles */
.project-hero {
    padding: 150px 0 100px;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.project-breadcrumb {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.project-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.project-breadcrumb span {
    margin: 0 0.5rem;
}

.project-title {
    font-size: 3rem;
    font-weight: 900;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.project-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.meta-item i {
    color: var(--primary-color);
}

.project-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.project-technologies h4 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.tech-list {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.project-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.project-featured-image {
    position: relative;
}

.main-image {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    cursor: pointer;
    transition: var(--transition);
}

.main-image:hover {
    transform: scale(1.02);
}

/* Project Details Section */
.project-details {
    background: rgba(0, 0, 0, 0.1);
}

.project-content-section {
    padding: 3rem;
}

.project-content-section h3 {
    color: white;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.content-text {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    font-size: 1.1rem;
}

.project-stats {
    margin-top: 2rem;
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Sidebar */
.project-sidebar {
    position: sticky;
    top: 100px;
}

.sidebar-card {
    padding: 2rem;
    margin-bottom: 2rem;
}

.sidebar-card h4 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.project-info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.project-info-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.project-info-list li:last-child {
    border-bottom: none;
}

.project-info-list strong {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.project-info-list span {
    color: white;
}

/* Share Buttons */
.share-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.share-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.2rem;
}

.share-btn.facebook { background: #1877f2; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.whatsapp { background: #25d366; }

.share-btn:hover {
    transform: translateY(-3px) scale(1.1);
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .lightbox-prev {
        left: -60px;
    }
    
    .lightbox-next {
        right: -60px;
    }
    
    .lightbox-close {
        top: -40px;
        right: -10px;
    }
    
    .lightbox-counter {
        bottom: -40px;
    }
    
    .project-title {
        font-size: 2rem;
    }
    
    .project-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .project-links {
        flex-direction: column;
        align-items: center;
    }
    
    .project-content-section {
        padding: 2rem;
    }
    
    .project-stats {
        flex-direction: column;
    }
    
    .share-buttons {
        flex-wrap: wrap;
    }
}
