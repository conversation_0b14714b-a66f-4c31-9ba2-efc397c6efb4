from django.db import models
from django.urls import reverse

class Technology(models.Model):
    name = models.CharField(max_length=50, unique=True)
    icon = models.CharField(max_length=100, blank=True, help_text="CSS class for icon")
    color = models.CharField(max_length=7, default="#000000", help_text="Hex color code")

    class Meta:
        verbose_name_plural = "Technologies"
        ordering = ['name']

    def __str__(self):
        return self.name

class ProjectCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Project Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

class Project(models.Model):
    STATUS_CHOICES = [
        ('completed', 'مكتمل'),
        ('in_progress', 'قيد التطوير'),
        ('planned', 'مخطط'),
    ]

    title = models.Cha<PERSON><PERSON><PERSON>(max_length=200, verbose_name="عنوان المشروع")
    slug = models.SlugField(unique=True, verbose_name="الرابط")
    description = models.TextField(verbose_name="وصف المشروع")
    short_description = models.CharField(max_length=300, verbose_name="وصف مختصر")
    category = models.ForeignKey(ProjectCategory, on_delete=models.CASCADE, verbose_name="الفئة")
    technologies = models.ManyToManyField(Technology, verbose_name="التقنيات المستخدمة")

    # Images
    featured_image = models.ImageField(upload_to='projects/featured/', verbose_name="الصورة الرئيسية")
    gallery_image_1 = models.ImageField(upload_to='projects/gallery/', blank=True, verbose_name="صورة المعرض 1")
    gallery_image_2 = models.ImageField(upload_to='projects/gallery/', blank=True, verbose_name="صورة المعرض 2")
    gallery_image_3 = models.ImageField(upload_to='projects/gallery/', blank=True, verbose_name="صورة المعرض 3")

    # Links
    live_url = models.URLField(blank=True, verbose_name="رابط المشروع المباشر")
    github_url = models.URLField(blank=True, verbose_name="رابط GitHub")
    app_store_url = models.URLField(blank=True, verbose_name="رابط App Store")
    play_store_url = models.URLField(blank=True, verbose_name="رابط Play Store")

    # Project details
    client = models.CharField(max_length=200, blank=True, verbose_name="العميل")
    duration = models.CharField(max_length=100, blank=True, verbose_name="مدة التطوير")
    team_size = models.PositiveIntegerField(default=1, verbose_name="حجم الفريق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='completed', verbose_name="الحالة")

    # SEO and metadata
    featured = models.BooleanField(default=False, verbose_name="مشروع مميز")
    order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-featured', 'order', '-created_at']
        verbose_name = "مشروع"
        verbose_name_plural = "المشاريع"

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('portfolio:project_detail', kwargs={'slug': self.slug})

    def get_gallery_images(self):
        """Return list of gallery images that are not empty"""
        images = []
        for img in [self.gallery_image_1, self.gallery_image_2, self.gallery_image_3]:
            if img:
                images.append(img)
        return images
