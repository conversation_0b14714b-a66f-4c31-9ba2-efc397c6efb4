# Local development settings for Codnet Website
# This file contains optimized settings for local development with the new theme

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', '*.ngrok.io']

# Database for local development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static files for development
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Static files collection (for production testing)
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files for development
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Cache for development (optimized for new theme)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'codnet-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# Session settings for better development experience
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = True

# Security settings for development
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Internationalization for Arabic support
USE_I18N = True
USE_L10N = True
USE_TZ = True
LANGUAGE_CODE = 'ar'
TIME_ZONE = 'Africa/Casablanca'

# Development-specific settings for the new theme
THEME_SETTINGS = {
    'ENABLE_AOS_ANIMATIONS': True,
    'ENABLE_LAZY_LOADING': True,
    'DISABLE_ANIMATIONS_ON_MOBILE': True,
    'THEME_COLOR': '#667eea',
    'BACKGROUND_COLOR': '#f8fafc',
}

# Logging for development (enhanced for debugging)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'debug.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'codnet_website': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Development tools
if DEBUG:
    # Add development middleware
    INTERNAL_IPS = ['127.0.0.1', 'localhost']

    # Performance monitoring for development
    PERFORMANCE_MONITORING = {
        'ENABLE_QUERY_LOGGING': True,
        'ENABLE_TEMPLATE_TIMING': True,
        'ENABLE_CACHE_MONITORING': True,
    }

# Custom settings for Codnet theme development
CODNET_THEME = {
    'PRIMARY_COLOR': '#2d3748',
    'SECONDARY_COLOR': '#4a5568',
    'ACCENT_COLOR': '#667eea',
    'BACKGROUND_PRIMARY': '#f8fafc',
    'BACKGROUND_SECONDARY': '#ffffff',
    'TEXT_PRIMARY': '#2d3748',
    'TEXT_SECONDARY': '#4a5568',
    'GRADIENT_PRIMARY': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'ENABLE_GLASS_EFFECT': True,
    'ENABLE_SMOOTH_SCROLLING': True,
}
