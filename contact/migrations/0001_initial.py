# Generated by Django 5.2.4 on 2025-07-21 23:55

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('is_replied', models.BooleanField(default=False, verbose_name='تم الرد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
            ],
            options={
                'verbose_name': 'رسالة اتصال',
                'verbose_name_plural': 'رسائل الاتصال',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectEstimation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العميل')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('company', models.CharField(blank=True, max_length=100, verbose_name='الشركة')),
                ('project_type', models.CharField(choices=[('ios_app', 'تطبيق iOS'), ('android_app', 'تطبيق Android'), ('web_app', 'تطبيق ويب'), ('website', 'موقع إلكتروني'), ('management_system', 'نظام إدارة'), ('ecommerce', 'متجر إلكتروني'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المشروع')),
                ('project_title', models.CharField(max_length=200, verbose_name='عنوان المشروع')),
                ('project_description', models.TextField(verbose_name='وصف المشروع')),
                ('key_features', models.TextField(verbose_name='الميزات الأساسية')),
                ('target_audience', models.CharField(blank=True, max_length=200, verbose_name='الجمهور المستهدف')),
                ('budget_range', models.CharField(blank=True, choices=[('under_5k', 'أقل من 5,000 درهم'), ('5k_10k', '5,000 - 10,000 درهم'), ('10k_25k', '10,000 - 25,000 درهم'), ('25k_50k', '25,000 - 50,000 درهم'), ('50k_100k', '50,000 - 100,000 درهم'), ('over_100k', 'أكثر من 100,000 درهم'), ('not_sure', 'غير محدد')], max_length=20, verbose_name='نطاق الميزانية')),
                ('duration_range', models.CharField(blank=True, choices=[('1_month', 'شهر واحد'), ('2_3_months', '2-3 أشهر'), ('3_6_months', '3-6 أشهر'), ('6_12_months', '6-12 شهر'), ('over_year', 'أكثر من سنة'), ('flexible', 'مرن')], max_length=20, verbose_name='المدة المطلوبة')),
                ('attachment', models.FileField(blank=True, upload_to='project_estimations/', verbose_name='ملف مرفق')),
                ('is_processed', models.BooleanField(default=False, verbose_name='تم المعالجة')),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='التكلفة المقدرة')),
                ('estimated_duration', models.CharField(blank=True, max_length=100, verbose_name='المدة المقدرة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
            ],
            options={
                'verbose_name': 'طلب تقدير مشروع',
                'verbose_name_plural': 'طلبات تقدير المشاريع',
                'ordering': ['-created_at'],
            },
        ),
    ]
