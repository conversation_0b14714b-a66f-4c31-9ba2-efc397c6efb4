from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
import requests
from .models import ContactMessage, ProjectEstimation
from .forms import ContactForm, ProjectEstimationForm

def contact(request):
    """صفحة الاتصال"""
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.')
            return redirect('contact:contact')
    else:
        form = ContactForm()

    context = {
        'form': form,
    }
    return render(request, 'contact/contact.html', context)

def project_estimation(request):
    """صفحة تقدير المشاريع"""
    if request.method == 'POST':
        form = ProjectEstimationForm(request.POST, request.FILES)
        if form.is_valid():
            estimation = form.save()

            # Send to Google Sheets (will implement later)
            send_to_google_sheets(estimation)

            messages.success(request, 'تم إرسال طلب التقدير بنجاح. سنتواصل معك خلال 24 ساعة.')
            return redirect('contact:estimation')
    else:
        form = ProjectEstimationForm()

    context = {
        'form': form,
    }
    return render(request, 'contact/project_estimation.html', context)

def send_to_google_sheets(estimation):
    """إرسال البيانات إلى Google Sheets"""
    # This will be implemented when Google Sheets URL is provided
    # For now, we'll just pass
    pass

@csrf_exempt
def contact_ajax(request):
    """AJAX endpoint لإرسال رسائل الاتصال"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            contact_message = ContactMessage.objects.create(
                name=data.get('name'),
                email=data.get('email'),
                phone=data.get('phone', ''),
                subject=data.get('subject'),
                message=data.get('message')
            )

            return JsonResponse({
                'success': True,
                'message': 'تم إرسال رسالتك بنجاح'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': 'حدث خطأ أثناء إرسال الرسالة'
            })

    return JsonResponse({'success': False, 'message': 'طريقة غير مسموحة'})
