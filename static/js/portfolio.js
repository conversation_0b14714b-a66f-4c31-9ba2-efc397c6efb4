// ===== PORTFOLIO PAGE JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    initProjectFilters();
    initProjectSearch();
    initProjectAnimations();
    initLightbox();
});

// ===== PROJECT FILTERS =====
function initProjectFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectItems = document.querySelectorAll('.project-item');
    const loadingOverlay = document.getElementById('loading-overlay');
    
    let currentCategory = 'all';
    let currentTechnology = 'all';
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-type');
            const filterValue = this.getAttribute('data-filter');
            
            // Update active state
            const siblingButtons = this.parentElement.querySelectorAll('.filter-btn');
            siblingButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update current filters
            if (filterType === 'category') {
                currentCategory = filterValue;
            } else if (filterType === 'technology') {
                currentTechnology = filterValue;
            }
            
            // Show loading
            showLoading();
            
            // Filter projects with animation
            setTimeout(() => {
                filterProjects(currentCategory, currentTechnology);
                hideLoading();
            }, 500);
        });
    });
}

function filterProjects(category, technology) {
    const projectItems = document.querySelectorAll('.project-item');
    
    projectItems.forEach((item, index) => {
        const itemCategory = item.getAttribute('data-category');
        const itemTechnologies = item.getAttribute('data-technologies').split(',');
        
        let showItem = true;
        
        // Check category filter
        if (category !== 'all' && itemCategory !== category) {
            showItem = false;
        }
        
        // Check technology filter
        if (technology !== 'all' && !itemTechnologies.includes(technology)) {
            showItem = false;
        }
        
        // Animate item visibility
        if (showItem) {
            setTimeout(() => {
                item.classList.remove('hidden');
                item.style.display = 'block';
            }, index * 100);
        } else {
            item.classList.add('hidden');
            setTimeout(() => {
                item.style.display = 'none';
            }, 300);
        }
    });
    
    // Update URL without page reload
    updateURL(category, technology);
}

function updateURL(category, technology) {
    const url = new URL(window.location);
    
    if (category !== 'all') {
        url.searchParams.set('category', category);
    } else {
        url.searchParams.delete('category');
    }
    
    if (technology !== 'all') {
        url.searchParams.set('technology', technology);
    } else {
        url.searchParams.delete('technology');
    }
    
    window.history.pushState({}, '', url);
}

// ===== PROJECT SEARCH =====
function initProjectSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    const projectItems = document.querySelectorAll('.project-item');
    
    let searchTimeout;
    
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        showLoading();
        
        setTimeout(() => {
            projectItems.forEach((item, index) => {
                const projectCard = item.querySelector('.project-card');
                const title = projectCard.querySelector('.project-title a').textContent.toLowerCase();
                const description = projectCard.querySelector('.project-description').textContent.toLowerCase();
                const technologies = item.getAttribute('data-technologies').toLowerCase();
                
                const matches = title.includes(searchTerm) || 
                              description.includes(searchTerm) || 
                              technologies.includes(searchTerm);
                
                if (matches || searchTerm === '') {
                    setTimeout(() => {
                        item.classList.remove('hidden');
                        item.style.display = 'block';
                    }, index * 50);
                } else {
                    item.classList.add('hidden');
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 200);
                }
            });
            
            hideLoading();
        }, 300);
        
        // Update URL
        const url = new URL(window.location);
        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        window.history.pushState({}, '', url);
    }
    
    // Search on input with debounce
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 500);
    });
    
    // Search on button click
    searchBtn.addEventListener('click', performSearch);
    
    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            performSearch();
        }
    });
}

// ===== PROJECT ANIMATIONS =====
function initProjectAnimations() {
    const projectCards = document.querySelectorAll('.project-card');
    
    // Hover effects
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.02)';
            
            // Animate tech tags
            const techTags = this.querySelectorAll('.tech-tag');
            techTags.forEach((tag, index) => {
                setTimeout(() => {
                    tag.style.transform = 'translateY(-3px)';
                }, index * 50);
            });
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            
            // Reset tech tags
            const techTags = this.querySelectorAll('.tech-tag');
            techTags.forEach(tag => {
                tag.style.transform = 'translateY(0)';
            });
        });
    });
    
    // Scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    projectCards.forEach(card => {
        observer.observe(card);
    });
}

// ===== LIGHTBOX =====
function initLightbox() {
    const galleryImages = document.querySelectorAll('[data-lightbox]');
    
    if (galleryImages.length === 0) return;
    
    // Create lightbox HTML
    const lightboxHTML = `
        <div class="lightbox-overlay" id="lightbox-overlay">
            <div class="lightbox-container">
                <button class="lightbox-close">&times;</button>
                <button class="lightbox-prev">&#8249;</button>
                <button class="lightbox-next">&#8250;</button>
                <img class="lightbox-image" src="" alt="">
                <div class="lightbox-counter">
                    <span class="current">1</span> / <span class="total">1</span>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', lightboxHTML);
    
    const lightboxOverlay = document.getElementById('lightbox-overlay');
    const lightboxImage = lightboxOverlay.querySelector('.lightbox-image');
    const lightboxClose = lightboxOverlay.querySelector('.lightbox-close');
    const lightboxPrev = lightboxOverlay.querySelector('.lightbox-prev');
    const lightboxNext = lightboxOverlay.querySelector('.lightbox-next');
    const currentCounter = lightboxOverlay.querySelector('.current');
    const totalCounter = lightboxOverlay.querySelector('.total');
    
    let currentImageIndex = 0;
    let images = [];
    
    // Collect all gallery images
    galleryImages.forEach((img, index) => {
        images.push({
            src: img.src,
            alt: img.alt || ''
        });
        
        img.addEventListener('click', function(e) {
            e.preventDefault();
            currentImageIndex = index;
            openLightbox();
        });
    });
    
    function openLightbox() {
        lightboxOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
        updateLightboxImage();
    }
    
    function closeLightbox() {
        lightboxOverlay.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    function updateLightboxImage() {
        const image = images[currentImageIndex];
        lightboxImage.src = image.src;
        lightboxImage.alt = image.alt;
        currentCounter.textContent = currentImageIndex + 1;
        totalCounter.textContent = images.length;
        
        // Show/hide navigation buttons
        lightboxPrev.style.display = currentImageIndex > 0 ? 'block' : 'none';
        lightboxNext.style.display = currentImageIndex < images.length - 1 ? 'block' : 'none';
    }
    
    function nextImage() {
        if (currentImageIndex < images.length - 1) {
            currentImageIndex++;
            updateLightboxImage();
        }
    }
    
    function prevImage() {
        if (currentImageIndex > 0) {
            currentImageIndex--;
            updateLightboxImage();
        }
    }
    
    // Event listeners
    lightboxClose.addEventListener('click', closeLightbox);
    lightboxNext.addEventListener('click', nextImage);
    lightboxPrev.addEventListener('click', prevImage);
    
    lightboxOverlay.addEventListener('click', function(e) {
        if (e.target === lightboxOverlay) {
            closeLightbox();
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!lightboxOverlay.classList.contains('active')) return;
        
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                prevImage();
                break;
            case 'ArrowRight':
                nextImage();
                break;
        }
    });
}

// ===== UTILITY FUNCTIONS =====
function showLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('active');
    }
}

function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('active');
    }
}

// ===== AJAX PROJECT FILTERING =====
function filterProjectsAjax(category, technology) {
    const url = new URL('/portfolio/ajax/filter/', window.location.origin);
    
    if (category !== 'all') {
        url.searchParams.set('category', category);
    }
    
    if (technology !== 'all') {
        url.searchParams.set('technology', technology);
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            updateProjectsGrid(data.projects);
        })
        .catch(error => {
            console.error('Error filtering projects:', error);
            hideLoading();
        });
}

function updateProjectsGrid(projects) {
    const container = document.getElementById('projects-container');
    
    if (projects.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="no-projects glass-card text-center">
                    <i class="fas fa-folder-open"></i>
                    <h3>لا توجد مشاريع</h3>
                    <p>لم يتم العثور على مشاريع تطابق معايير البحث الخاصة بك.</p>
                </div>
            </div>
        `;
    } else {
        // Generate project cards HTML
        let html = '';
        projects.forEach((project, index) => {
            html += generateProjectCardHTML(project, index);
        });
        container.innerHTML = html;
        
        // Reinitialize animations
        initProjectAnimations();
    }
    
    hideLoading();
}

function generateProjectCardHTML(project, index) {
    return `
        <div class="col-lg-4 col-md-6 mb-4 project-item">
            <div class="project-card glass-card scale-in" style="--delay: ${(index + 1) * 100}ms">
                <div class="project-image">
                    ${project.featured_image ? 
                        `<img src="${project.featured_image}" alt="${project.title}" loading="lazy">` :
                        `<div class="placeholder-image"><i class="fas fa-image"></i></div>`
                    }
                    <div class="project-overlay">
                        <div class="project-actions">
                            <a href="${project.url}" class="action-btn">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="project-content">
                    <div class="project-meta">
                        <span class="project-category">${project.category}</span>
                    </div>
                    <h3 class="project-title">
                        <a href="${project.url}">${project.title}</a>
                    </h3>
                    <p class="project-description">${project.short_description}</p>
                    <div class="project-technologies">
                        ${project.technologies.slice(0, 4).map(tech => 
                            `<span class="tech-tag">${tech}</span>`
                        ).join('')}
                        ${project.technologies.length > 4 ? 
                            `<span class="tech-more">+${project.technologies.length - 4}</span>` : ''
                        }
                    </div>
                </div>
            </div>
        </div>
    `;
}
