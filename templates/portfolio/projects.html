{% extends 'base.html' %}
{% load static %}

{% block title %}المشاريع - Codnet{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/portfolio.css' %}">
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title fade-in">مشاريعنا</h1>
                <p class="page-subtitle fade-in">
                    اكتشف مجموعة من أفضل أعمالنا في تطوير البرمجيات وتطبيقات الهاتف
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filters-container glass-card fade-in">
                    <div class="filter-group">
                        <label class="filter-label">الفئة:</label>
                        <div class="filter-buttons">
                            <button class="filter-btn {% if not current_category %}active{% endif %}" 
                                    data-filter="all" data-type="category">
                                جميع الفئات
                            </button>
                            {% for category in categories %}
                            <button class="filter-btn {% if current_category == category.name %}active{% endif %}" 
                                    data-filter="{{ category.name }}" data-type="category">
                                {{ category.name }}
                            </button>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">التقنية:</label>
                        <div class="filter-buttons">
                            <button class="filter-btn {% if not current_technology %}active{% endif %}" 
                                    data-filter="all" data-type="technology">
                                جميع التقنيات
                            </button>
                            {% for tech in technologies %}
                            <button class="filter-btn {% if current_technology == tech.name %}active{% endif %}" 
                                    data-filter="{{ tech.name }}" data-type="technology">
                                {{ tech.name }}
                            </button>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="search-group">
                        <div class="search-container">
                            <input type="text" class="search-input" placeholder="ابحث في المشاريع..." 
                                   value="{{ search_query|default:'' }}">
                            <button class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="projects-grid-section section-padding">
    <div class="container">
        <div class="row" id="projects-container">
            {% for project in projects %}
            <div class="col-lg-4 col-md-6 mb-4 project-item" 
                 data-category="{{ project.category.name }}" 
                 data-technologies="{% for tech in project.technologies.all %}{{ tech.name }}{% if not forloop.last %},{% endif %}{% endfor %}">
                <div class="project-card glass-card scale-in" style="--delay: {{ forloop.counter0|add:1 }}00ms">
                    <div class="project-image">
                        {% if project.featured_image %}
                        <img src="{{ project.featured_image.url }}" alt="{{ project.title }}" loading="lazy">
                        {% else %}
                        <div class="placeholder-image">
                            <i class="fas fa-image"></i>
                        </div>
                        {% endif %}
                        
                        <div class="project-overlay">
                            <div class="project-actions">
                                <a href="{{ project.get_absolute_url }}" class="action-btn">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if project.live_url %}
                                <a href="{{ project.live_url }}" target="_blank" class="action-btn">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                {% endif %}
                                {% if project.github_url %}
                                <a href="{{ project.github_url }}" target="_blank" class="action-btn">
                                    <i class="fab fa-github"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if project.featured %}
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                            مميز
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="project-content">
                        <div class="project-meta">
                            <span class="project-category">{{ project.category.name }}</span>
                            <span class="project-status status-{{ project.status }}">
                                {% if project.status == 'completed' %}مكتمل
                                {% elif project.status == 'in_progress' %}قيد التطوير
                                {% else %}مخطط{% endif %}
                            </span>
                        </div>
                        
                        <h3 class="project-title">
                            <a href="{{ project.get_absolute_url }}">{{ project.title }}</a>
                        </h3>
                        
                        <p class="project-description">{{ project.short_description }}</p>
                        
                        <div class="project-technologies">
                            {% for tech in project.technologies.all|slice:":4" %}
                            <span class="tech-tag" style="background-color: {{ tech.color }}20; color: {{ tech.color }}">
                                {% if tech.icon %}<i class="{{ tech.icon }}"></i>{% endif %}
                                {{ tech.name }}
                            </span>
                            {% endfor %}
                            {% if project.technologies.count > 4 %}
                            <span class="tech-more">+{{ project.technologies.count|add:"-4" }}</span>
                            {% endif %}
                        </div>
                        
                        <div class="project-footer">
                            {% if project.client %}
                            <div class="project-client">
                                <i class="fas fa-user"></i>
                                {{ project.client }}
                            </div>
                            {% endif %}
                            {% if project.duration %}
                            <div class="project-duration">
                                <i class="fas fa-clock"></i>
                                {{ project.duration }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="no-projects glass-card text-center">
                    <i class="fas fa-folder-open"></i>
                    <h3>لا توجد مشاريع</h3>
                    <p>لم يتم العثور على مشاريع تطابق معايير البحث الخاصة بك.</p>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if projects.has_other_pages %}
        <div class="row">
            <div class="col-12">
                <nav class="pagination-nav">
                    <ul class="pagination">
                        {% if projects.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ projects.previous_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_technology %}&technology={{ current_technology }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for num in projects.paginator.page_range %}
                        {% if projects.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > projects.number|add:'-3' and num < projects.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_technology %}&technology={{ current_technology }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if projects.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ projects.next_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_technology %}&technology={{ current_technology }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner">
        <div class="spinner"></div>
        <p>جاري التحميل...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/portfolio.js' %}"></script>
{% endblock %}
