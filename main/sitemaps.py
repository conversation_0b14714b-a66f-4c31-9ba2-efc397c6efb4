from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from portfolio.models import Project
from team.models import TeamMember

class StaticViewSitemap(Sitemap):
    priority = 0.5
    changefreq = 'weekly'

    def items(self):
        return ['main:home', 'portfolio:projects', 'team:team', 'contact:contact', 'contact:estimation']

    def location(self, item):
        return reverse(item)

class ProjectSitemap(Sitemap):
    changefreq = "monthly"
    priority = 0.8

    def items(self):
        return Project.objects.filter(status='completed')

    def lastmod(self, obj):
        return obj.updated_at

    def location(self, obj):
        return obj.get_absolute_url()

# Sitemap dictionary
sitemaps = {
    'static': StaticViewSitemap,
    'projects': ProjectSitemap,
}
