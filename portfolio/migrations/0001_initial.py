# Generated by Django 5.2.4 on 2025-07-21 23:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name_plural': 'Project Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Technology',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('icon', models.Char<PERSON>ield(blank=True, help_text='CSS class for icon', max_length=100)),
                ('color', models.Char<PERSON>ield(default='#000000', help_text='Hex color code', max_length=7)),
            ],
            options={
                'verbose_name_plural': 'Technologies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المشروع')),
                ('slug', models.SlugField(unique=True, verbose_name='الرابط')),
                ('description', models.TextField(verbose_name='وصف المشروع')),
                ('short_description', models.CharField(max_length=300, verbose_name='وصف مختصر')),
                ('featured_image', models.ImageField(upload_to='projects/featured/', verbose_name='الصورة الرئيسية')),
                ('gallery_image_1', models.ImageField(blank=True, upload_to='projects/gallery/', verbose_name='صورة المعرض 1')),
                ('gallery_image_2', models.ImageField(blank=True, upload_to='projects/gallery/', verbose_name='صورة المعرض 2')),
                ('gallery_image_3', models.ImageField(blank=True, upload_to='projects/gallery/', verbose_name='صورة المعرض 3')),
                ('live_url', models.URLField(blank=True, verbose_name='رابط المشروع المباشر')),
                ('github_url', models.URLField(blank=True, verbose_name='رابط GitHub')),
                ('app_store_url', models.URLField(blank=True, verbose_name='رابط App Store')),
                ('play_store_url', models.URLField(blank=True, verbose_name='رابط Play Store')),
                ('client', models.CharField(blank=True, max_length=200, verbose_name='العميل')),
                ('duration', models.CharField(blank=True, max_length=100, verbose_name='مدة التطوير')),
                ('team_size', models.PositiveIntegerField(default=1, verbose_name='حجم الفريق')),
                ('status', models.CharField(choices=[('completed', 'مكتمل'), ('in_progress', 'قيد التطوير'), ('planned', 'مخطط')], default='completed', max_length=20, verbose_name='الحالة')),
                ('featured', models.BooleanField(default=False, verbose_name='مشروع مميز')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='portfolio.projectcategory', verbose_name='الفئة')),
                ('technologies', models.ManyToManyField(to='portfolio.technology', verbose_name='التقنيات المستخدمة')),
            ],
            options={
                'verbose_name': 'مشروع',
                'verbose_name_plural': 'المشاريع',
                'ordering': ['-featured', 'order', '-created_at'],
            },
        ),
    ]
