from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.core.paginator import Paginator
from .models import Project, ProjectCategory, Technology

def projects(request):
    """صفحة عرض جميع المشاريع"""
    projects_list = Project.objects.all()
    categories = ProjectCategory.objects.all()
    technologies = Technology.objects.all()

    # Filter by category
    category_filter = request.GET.get('category')
    if category_filter:
        projects_list = projects_list.filter(category__name=category_filter)

    # Filter by technology
    tech_filter = request.GET.get('technology')
    if tech_filter:
        projects_list = projects_list.filter(technologies__name=tech_filter)

    # Search
    search_query = request.GET.get('search')
    if search_query:
        projects_list = projects_list.filter(
            title__icontains=search_query
        ).distinct()

    # Pagination
    paginator = Paginator(projects_list, 9)  # 9 projects per page
    page_number = request.GET.get('page')
    projects_page = paginator.get_page(page_number)

    context = {
        'projects': projects_page,
        'categories': categories,
        'technologies': technologies,
        'current_category': category_filter,
        'current_technology': tech_filter,
        'search_query': search_query,
    }
    return render(request, 'portfolio/projects.html', context)

def project_detail(request, slug):
    """صفحة تفاصيل المشروع"""
    project = get_object_or_404(Project, slug=slug)
    related_projects = Project.objects.filter(
        category=project.category
    ).exclude(id=project.id)[:3]

    context = {
        'project': project,
        'related_projects': related_projects,
    }
    return render(request, 'portfolio/project_detail.html', context)

def filter_projects_ajax(request):
    """AJAX endpoint لتصفية المشاريع"""
    category = request.GET.get('category')
    technology = request.GET.get('technology')

    projects = Project.objects.all()

    if category and category != 'all':
        projects = projects.filter(category__name=category)

    if technology and technology != 'all':
        projects = projects.filter(technologies__name=technology)

    projects_data = []
    for project in projects:
        projects_data.append({
            'id': project.id,
            'title': project.title,
            'short_description': project.short_description,
            'featured_image': project.featured_image.url if project.featured_image else '',
            'category': project.category.name,
            'technologies': [tech.name for tech in project.technologies.all()],
            'url': project.get_absolute_url(),
        })

    return JsonResponse({'projects': projects_data})
