{% extends 'base.html' %}
{% load static %}

{% block title %}تقدير المشروع - Codnet{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/contact.css' %}">
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="page-title fade-in">تقدير المشروع</h1>
                <p class="page-subtitle fade-in">
                    احصل على تقدير مجاني ومفصل لمشروعك خلال 24 ساعة
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Estimation Process -->
<section class="estimation-process section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title fade-in">كيف نعمل</h2>
                <p class="section-subtitle fade-in">
                    عملية بسيطة وشفافة للحصول على تقدير دقيق لمشروعك
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step glass-card text-center slide-up" style="--delay: 100ms">
                    <div class="step-number">1</div>
                    <div class="step-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h4 class="step-title">املأ النموذج</h4>
                    <p class="step-description">
                        قدم تفاصيل مشروعك والمتطلبات الأساسية
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step glass-card text-center slide-up" style="--delay: 200ms">
                    <div class="step-number">2</div>
                    <div class="step-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h4 class="step-title">تحليل المتطلبات</h4>
                    <p class="step-description">
                        نقوم بدراسة وتحليل متطلبات مشروعك بعناية
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step glass-card text-center slide-up" style="--delay: 300ms">
                    <div class="step-number">3</div>
                    <div class="step-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h4 class="step-title">حساب التكلفة</h4>
                    <p class="step-description">
                        نحسب التكلفة والوقت المطلوب بناءً على خبرتنا
                    </p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="process-step glass-card text-center slide-up" style="--delay: 400ms">
                    <div class="step-number">4</div>
                    <div class="step-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <h4 class="step-title">إرسال التقدير</h4>
                    <p class="step-description">
                        نرسل لك تقديراً مفصلاً خلال 24 ساعة
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Estimation Form -->
<section class="estimation-form-section section-padding">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container glass-card fade-in">
                    <div class="form-header text-center">
                        <h3 class="form-title">نموذج تقدير المشروع</h3>
                        <p class="form-subtitle">
                            املأ النموذج أدناه وسنتواصل معك خلال 24 ساعة بتقدير مفصل
                        </p>
                    </div>
                    
                    {% if messages %}
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data" id="estimationForm" class="estimation-form">
                        {% csrf_token %}
                        
                        <!-- Personal Information -->
                        <div class="form-section">
                            <h4 class="section-title">معلومات شخصية</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.name }}
                                        <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                                        {% if form.name.errors %}
                                            <div class="form-error">{{ form.name.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.email }}
                                        <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                                        {% if form.email.errors %}
                                            <div class="form-error">{{ form.email.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.phone }}
                                        <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                                        {% if form.phone.errors %}
                                            <div class="form-error">{{ form.phone.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.company }}
                                        <label for="{{ form.company.id_for_label }}">{{ form.company.label }}</label>
                                        {% if form.company.errors %}
                                            <div class="form-error">{{ form.company.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Project Information -->
                        <div class="form-section">
                            <h4 class="section-title">معلومات المشروع</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.project_type }}
                                        <label for="{{ form.project_type.id_for_label }}">{{ form.project_type.label }}</label>
                                        {% if form.project_type.errors %}
                                            <div class="form-error">{{ form.project_type.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.project_title }}
                                        <label for="{{ form.project_title.id_for_label }}">{{ form.project_title.label }}</label>
                                        {% if form.project_title.errors %}
                                            <div class="form-error">{{ form.project_title.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                {{ form.project_description }}
                                <label for="{{ form.project_description.id_for_label }}">{{ form.project_description.label }}</label>
                                {% if form.project_description.errors %}
                                    <div class="form-error">{{ form.project_description.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                {{ form.key_features }}
                                <label for="{{ form.key_features.id_for_label }}">{{ form.key_features.label }}</label>
                                {% if form.key_features.errors %}
                                    <div class="form-error">{{ form.key_features.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                {{ form.target_audience }}
                                <label for="{{ form.target_audience.id_for_label }}">{{ form.target_audience.label }}</label>
                                {% if form.target_audience.errors %}
                                    <div class="form-error">{{ form.target_audience.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Budget and Timeline -->
                        <div class="form-section">
                            <h4 class="section-title">الميزانية والجدول الزمني</h4>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.budget_range }}
                                        <label for="{{ form.budget_range.id_for_label }}">{{ form.budget_range.label }}</label>
                                        {% if form.budget_range.errors %}
                                            <div class="form-error">{{ form.budget_range.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.duration_range }}
                                        <label for="{{ form.duration_range.id_for_label }}">{{ form.duration_range.label }}</label>
                                        {% if form.duration_range.errors %}
                                            <div class="form-error">{{ form.duration_range.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- File Attachment -->
                        <div class="form-section">
                            <h4 class="section-title">ملفات مرفقة (اختياري)</h4>
                            
                            <div class="form-group">
                                <div class="file-upload-area" id="fileUploadArea">
                                    {{ form.attachment }}
                                    <div class="file-upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>اسحب الملفات هنا أو انقر للاختيار</p>
                                        <small>PDF, DOC, DOCX, JPG, PNG (حد أقصى 10MB)</small>
                                    </div>
                                </div>
                                {% if form.attachment.errors %}
                                    <div class="form-error">{{ form.attachment.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="form-submit text-center">
                            <button type="submit" class="btn-primary-custom btn-submit">
                                <i class="fas fa-paper-plane"></i>
                                <span class="btn-text">إرسال طلب التقدير</span>
                                <span class="btn-loading" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري الإرسال...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="why-choose-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title fade-in">لماذا تختار Codnet؟</h2>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card glass-card text-center slide-up" style="--delay: 100ms">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="feature-title">سرعة في التقدير</h4>
                    <p class="feature-description">
                        نقدم تقديراً مفصلاً خلال 24 ساعة من استلام طلبك
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card glass-card text-center slide-up" style="--delay: 200ms">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">شفافية كاملة</h4>
                    <p class="feature-description">
                        تقدير واضح ومفصل بدون رسوم خفية أو مفاجآت
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card glass-card text-center slide-up" style="--delay: 300ms">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 class="feature-title">فريق خبير</h4>
                    <p class="feature-description">
                        فريق من المطورين ذوي الخبرة في مختلف التقنيات
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/contact.js' %}"></script>
{% endblock %}
