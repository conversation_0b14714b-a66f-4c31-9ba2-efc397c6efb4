from django.contrib import admin
from .models import CompanyInfo, Service, Testimonial

@admin.register(CompanyInfo)
class CompanyInfoAdmin(admin.ModelAdmin):
    fieldsets = (
        ('معلومات الشركة', {
            'fields': ('name', 'tagline', 'description', 'mission', 'vision')
        }),
        ('معلومات الاتصال', {
            'fields': ('email', 'phone', 'address')
        }),
        ('وسائل التواصل الاجتماعي', {
            'fields': ('linkedin_url', 'facebook_url', 'twitter_url', 'instagram_url')
        }),
        ('الشعار والهوية البصرية', {
            'fields': ('logo', 'favicon')
        }),
    )

    def has_add_permission(self, request):
        # Allow adding only if no instance exists
        return not CompanyInfo.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion
        return False

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ['title', 'icon', 'color', 'is_active', 'order']
    list_filter = ['is_active']
    search_fields = ['title', 'description']
    list_editable = ['is_active', 'order', 'icon', 'color']

@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['client_name', 'client_company', 'rating', 'is_featured', 'is_active', 'order']
    list_filter = ['rating', 'is_featured', 'is_active']
    search_fields = ['client_name', 'client_company', 'testimonial_text']
    list_editable = ['is_featured', 'is_active', 'order']

    fieldsets = (
        ('معلومات العميل', {
            'fields': ('client_name', 'client_position', 'client_company', 'client_photo')
        }),
        ('الشهادة', {
            'fields': ('testimonial_text', 'rating')
        }),
        ('إعدادات العرض', {
            'fields': ('is_featured', 'is_active', 'order')
        }),
    )
