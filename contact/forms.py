from django import forms
from .models import ContactMessage, ProjectEstimation

class ContactForm(forms.ModelForm):
    class Meta:
        model = ContactMessage
        fields = ['name', 'email', 'phone', 'subject', 'message']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'اسمك الكامل',
                'required': True
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'بريدك الإلكتروني',
                'required': True
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'رقم هاتفك (اختياري)',
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'موضوع الرسالة',
                'required': True
            }),
            'message': forms.Textarea(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'اكتب رسالتك هنا...',
                'rows': 5,
                'required': True
            }),
        }
        labels = {
            'name': 'الاسم',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'subject': 'الموضوع',
            'message': 'الرسالة',
        }

class ProjectEstimationForm(forms.ModelForm):
    class Meta:
        model = ProjectEstimation
        fields = [
            'name', 'email', 'phone', 'company',
            'project_type', 'project_title', 'project_description',
            'key_features', 'target_audience',
            'budget_range', 'duration_range', 'attachment'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'اسمك الكامل',
                'required': True
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'بريدك الإلكتروني',
                'required': True
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'رقم هاتفك',
                'required': True
            }),
            'company': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'اسم الشركة (اختياري)',
            }),
            'project_type': forms.Select(attrs={
                'class': 'form-control glass-input',
                'required': True
            }),
            'project_title': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'عنوان المشروع',
                'required': True
            }),
            'project_description': forms.Textarea(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'وصف تفصيلي للمشروع...',
                'rows': 4,
                'required': True
            }),
            'key_features': forms.Textarea(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'الميزات الأساسية المطلوبة...',
                'rows': 3,
                'required': True
            }),
            'target_audience': forms.TextInput(attrs={
                'class': 'form-control glass-input',
                'placeholder': 'الجمهور المستهدف (اختياري)',
            }),
            'budget_range': forms.Select(attrs={
                'class': 'form-control glass-input',
            }),
            'duration_range': forms.Select(attrs={
                'class': 'form-control glass-input',
            }),
            'attachment': forms.FileInput(attrs={
                'class': 'form-control glass-input',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png',
            }),
        }
        labels = {
            'name': 'الاسم',
            'email': 'البريد الإلكتروني',
            'phone': 'رقم الهاتف',
            'company': 'الشركة',
            'project_type': 'نوع المشروع',
            'project_title': 'عنوان المشروع',
            'project_description': 'وصف المشروع',
            'key_features': 'الميزات الأساسية',
            'target_audience': 'الجمهور المستهدف',
            'budget_range': 'نطاق الميزانية',
            'duration_range': 'المدة المطلوبة',
            'attachment': 'ملف مرفق',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add custom validation or modify fields if needed
        self.fields['budget_range'].empty_label = "اختر نطاق الميزانية"
        self.fields['duration_range'].empty_label = "اختر المدة المطلوبة"
