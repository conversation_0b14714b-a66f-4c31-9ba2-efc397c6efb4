/* ===== TECH CALM THEME - الهدوء التكنولوجي ===== */
/* ثيم مخصص لموقع Codnet بأسلوب مجرد فريد مع أنيميشن خطي */

:root {
    /* الألوان الأساسية */
    --primary-dark-blue: #1A237E;
    --primary-light-blue: #00BCD4;
    
    /* ألوان التركيز */
    --vibrant-orange: #FF8C00;
    --neon-green: #00FF7F;
    
    /* ألوان النصوص والخلفيات */
    --pure-white: #FFFFFF;
    --light-gray: #E0E0E0;
    --dark-gray: #424242;
    
    /* تدرجات مخصصة */
    --gradient-primary: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-light-blue) 100%);
    --gradient-accent: linear-gradient(45deg, var(--vibrant-orange) 0%, var(--neon-green) 100%);
    --gradient-subtle: linear-gradient(180deg, var(--light-gray) 0%, var(--pure-white) 100%);
    
    /* ظلال وتأثيرات */
    --shadow-soft: 0 4px 20px rgba(26, 35, 126, 0.1);
    --shadow-medium: 0 8px 30px rgba(26, 35, 126, 0.15);
    --shadow-strong: 0 15px 50px rgba(26, 35, 126, 0.2);
    --glow-neon: 0 0 20px rgba(0, 255, 127, 0.3);
    
    /* مسافات احترافية */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 40px;
    --spacing-xl: 60px;
    --spacing-xxl: 80px;
    --spacing-section: 100px;
    
    /* انتقالات سلسة */
    --transition-fast: 0.2s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    
    /* حدود وزوايا */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-width: 2px;
}

/* ===== RESET AND BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
    color: var(--pure-white);
    background: var(--primary-dark-blue);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* ===== NAVIGATION BAR - شريط التصفح الثابت مع أنيميشن خطي ===== */
.tech-calm-navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(26, 35, 126, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 188, 212, 0.2);
    z-index: 1000;
    padding: var(--spacing-sm) 0;
    transition: all var(--transition-normal);
}

.tech-calm-navbar.scrolled {
    background: rgba(26, 35, 126, 0.98);
    box-shadow: var(--shadow-medium);
    padding: var(--spacing-xs) 0;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--pure-white);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.navbar-brand-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--pure-white);
    font-size: 1.2rem;
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    align-items: center;
}

.navbar-link {
    position: relative;
    color: var(--pure-white);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: color var(--transition-normal);
    overflow: hidden;
}

/* أنيميشن خطي للروابط */
.navbar-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.navbar-link:hover {
    color: var(--primary-light-blue);
}

.navbar-link:hover::after {
    width: 100%;
}

.navbar-link.active::after {
    width: 100%;
    background: var(--neon-green);
    box-shadow: var(--glow-neon);
}

/* ===== BUTTONS - أزرار محسنة ===== */
.btn-tech-primary {
    background: var(--gradient-accent);
    color: var(--pure-white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.btn-tech-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-tech-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-strong);
    color: var(--pure-white);
}

.btn-tech-primary:hover::before {
    left: 100%;
}

.btn-tech-secondary {
    background: transparent;
    color: var(--primary-light-blue);
    border: var(--border-width) solid var(--primary-light-blue);
    padding: calc(var(--spacing-sm) - var(--border-width)) calc(var(--spacing-lg) - var(--border-width));
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-tech-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-light-blue);
    transition: width var(--transition-normal);
    z-index: -1;
}

.btn-tech-secondary:hover {
    color: var(--pure-white);
    transform: translateY(-2px);
}

.btn-tech-secondary:hover::before {
    width: 100%;
}

/* ===== CARDS - بطاقات مجردة ===== */
.tech-calm-card {
    background: var(--light-gray);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-soft);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.tech-calm-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
    transform-origin: left;
}

.tech-calm-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
}

.tech-calm-card:hover::before {
    transform: scaleX(1);
}

.card-title {
    color: var(--primary-dark-blue);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.card-text {
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

/* ===== SECTIONS - أقسام مع مسافات احترافية ===== */
.tech-calm-section {
    padding: var(--spacing-section) 0;
    position: relative;
}

.tech-calm-section.alternate {
    background: var(--gradient-subtle);
    color: var(--primary-dark-blue);
}

.tech-calm-section.alternate .section-title {
    color: var(--primary-dark-blue);
}

.section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-title {
    font-size: 3rem;
    font-weight: 900;
    text-align: center;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -var(--spacing-sm);
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto var(--spacing-xxl);
    line-height: 1.7;
}

/* ===== GEOMETRIC SEPARATORS - فواصل هندسية ===== */
.geometric-separator {
    height: 2px;
    background: var(--gradient-accent);
    margin: var(--spacing-xxl) auto;
    max-width: 200px;
    position: relative;
}

.geometric-separator::before,
.geometric-separator::after {
    content: '';
    position: absolute;
    top: -4px;
    width: 10px;
    height: 10px;
    background: var(--neon-green);
    border-radius: 50%;
}

.geometric-separator::before {
    left: -5px;
}

.geometric-separator::after {
    right: -5px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .navbar-nav {
        gap: var(--spacing-md);
    }
    
    .navbar-link {
        padding: var(--spacing-xs);
    }
    
    .section-title {
        font-size: 2.2rem;
    }
    
    .tech-calm-section {
        padding: var(--spacing-xxl) 0;
    }
    
    .tech-calm-card {
        padding: var(--spacing-lg);
    }
    
    .btn-tech-primary,
    .btn-tech-secondary {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-fade-up {
    animation: fadeInUp 0.6s ease-out;
}
