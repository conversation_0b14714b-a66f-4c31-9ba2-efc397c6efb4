#!/bin/bash

# Codnet Website Quick Start Script

echo "🚀 بدء تشغيل موقع Codnet..."

# Check if virtual environment exists
if [ ! -d "codnet_env" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv codnet_env
fi

# Activate virtual environment
echo "🔧 تفعيل البيئة الافتراضية..."
source codnet_env/bin/activate

# Install requirements
echo "📚 تثبيت المتطلبات..."
pip install -q django pillow requests python-decouple

# Check if database exists
if [ ! -f "db.sqlite3" ]; then
    echo "🗄️ إنشاء قاعدة البيانات..."
    python manage.py makemigrations
    python manage.py migrate
    
    echo "👤 إنشاء مستخدم إداري..."
    echo "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123')" | python manage.py shell
    
    echo "📊 إنشاء البيانات التجريبية..."
    python manage.py create_sample_data
fi

# Collect static files
echo "📁 جمع الملفات الثابتة..."
python manage.py collectstatic --noinput

echo "✅ تم الإعداد بنجاح!"
echo ""
echo "🌐 تشغيل الخادم..."
echo "📍 الموقع: http://127.0.0.1:8000"
echo "🔐 لوحة الإدارة: http://127.0.0.1:8000/admin"
echo "👤 المستخدم: admin"
echo "🔑 كلمة المرور: admin123"
echo ""
echo "⏹️  للإيقاف: اضغط Ctrl+C"
echo ""

# Start development server
python manage.py runserver
