# Generated by Django 5.2.4 on 2025-07-21 23:55

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('position', models.CharField(max_length=100, verbose_name='المنصب')),
                ('bio', models.TextField(verbose_name='نبذة شخصية')),
                ('photo', models.ImageField(upload_to='team/', verbose_name='الصورة الشخصية')),
                ('skills', models.TextField(help_text='مهارات مفصولة بفواصل', verbose_name='المهارات')),
                ('experience_years', models.PositiveIntegerField(verbose_name='سنوات الخبرة')),
                ('linkedin_url', models.URLField(blank=True, verbose_name='رابط LinkedIn')),
                ('github_url', models.URLField(blank=True, verbose_name='رابط GitHub')),
                ('twitter_url', models.URLField(blank=True, verbose_name='رابط Twitter')),
                ('website_url', models.URLField(blank=True, verbose_name='الموقع الشخصي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'عضو الفريق',
                'verbose_name_plural': 'أعضاء الفريق',
                'ordering': ['order', 'name'],
            },
        ),
    ]
